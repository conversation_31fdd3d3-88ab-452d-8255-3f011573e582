"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationsModuleData = void 0;
class InitiateRequest {
    path = '/v1/verifications/initiate';
    httpMethod = 'POST';
    returnType;
    body;
}
class Step1Request {
    path = '/v1/verifications/steps/1';
    httpMethod = 'POST';
    returnType;
    body;
}
class StepNoRequest {
    path = '/v1/verifications/steps/:stepNo';
    httpMethod = 'POST';
    returnType;
    body;
    params;
}
class VerificationsModuleData {
    requests = {
        initiate: new InitiateRequest(),
        step1: new Step1Request(),
        stepNo: new StepNoRequest(),
    };
}
exports.VerificationsModuleData = VerificationsModuleData;
//# sourceMappingURL=verifications.module-data.js.map