import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { EMethodsForABTest, EVerificationNames, EVerificationSubTypes } from './verification-configuration';
import { FactoryInitiateDTO } from '../verification-services/factory-initiate.dto';
import { FlowStepDTO } from '../verification-services/flow-step.dto';
import { EVerificationErrors, IInitiateResponse, IVerificationAccepted, IVerificationComplete, TABTestDecoration, TVerificationInitiateConfig, TVerificationService } from '../verification-services/types';
import { TJson } from '../common/types';
import { EABResponse } from '@superawesome/freekws-ab-testing/ab-test/types';
export declare class InitiateInputDTO extends FactoryInitiateDTO {
    verificationExclusions: EVerificationNames[];
}
export declare class TVerificationABTestItem implements TABTestDecoration {
    testName: string;
    testGroup: EABResponse;
    testMethod: EMethodsForABTest | EABResponse.NoTest;
}
export declare class AvailableVerification implements TVerificationService {
    verification: EVerificationNames;
    config: TVerificationInitiateConfig;
}
export declare class InitiateOutputDTO implements IInitiateResponse {
    availableVerifications: AvailableVerification[];
    requiredFields: SchemaObject;
    stateToken: string;
    nextStep: number;
    abTests?: TVerificationABTestItem[];
}
export declare class StepOutputDTO implements Partial<Omit<IVerificationComplete, 'status'>>, Partial<Omit<IVerificationAccepted, 'status'>> {
    verified?: boolean;
    transactionId?: string;
    errorCode?: EVerificationErrors;
    requiredFields?: SchemaObject;
    stateToken?: string;
    nextStep?: number;
    nextStepData?: TJson;
    isChargeable?: boolean;
    verificationSubType?: EVerificationSubTypes;
    isIdentifiedMinor?: boolean;
}
export declare class FirstStepInputDTO implements FlowStepDTO<TJson> {
    stateToken: string;
    selectedVerification: EVerificationNames;
    verificationPayload: TJson;
}
export declare class OtherStepInputDTO implements FlowStepDTO<TJson> {
    stateToken: string;
    verificationPayload: TJson;
    minimumAge: number;
    maximumAge?: number;
}
