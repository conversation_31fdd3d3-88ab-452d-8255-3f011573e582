"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtherStepInputDTO = exports.FirstStepInputDTO = exports.StepOutputDTO = exports.InitiateOutputDTO = exports.AvailableVerification = exports.TVerificationABTestItem = exports.InitiateInputDTO = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const verification_configuration_1 = require("./verification-configuration");
const factory_initiate_dto_1 = require("../verification-services/factory-initiate.dto");
const types_1 = require("../verification-services/types");
const class_transformer_1 = require("class-transformer");
const types_2 = require("@superawesome/freekws-ab-testing/ab-test/types");
class InitiateInputDTO extends factory_initiate_dto_1.FactoryInitiateDTO {
    verificationExclusions;
}
exports.InitiateInputDTO = InitiateInputDTO;
__decorate([
    (0, class_validator_1.IsIn)(Object.values(verification_configuration_1.EVerificationNames), {
        each: true,
    }),
    (0, swagger_1.ApiProperty)({
        example: '["veratad"]',
        description: 'List of verification methods to exclude',
    }),
    __metadata("design:type", Array)
], InitiateInputDTO.prototype, "verificationExclusions", void 0);
class TVerificationABTestItem {
    testName;
    testGroup;
    testMethod;
}
exports.TVerificationABTestItem = TVerificationABTestItem;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        example: `exampleTest`,
        description: 'The test applied to the initiate output set of verification methods',
    }),
    __metadata("design:type", String)
], TVerificationABTestItem.prototype, "testName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        example: 'A',
        description: `The group that 'won' the applied test.`,
    }),
    __metadata("design:type", String)
], TVerificationABTestItem.prototype, "testGroup", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        example: 'veratad',
        description: `The method mapped to the winning group`,
    }),
    __metadata("design:type", String)
], TVerificationABTestItem.prototype, "testMethod", void 0);
class AvailableVerification {
    verification;
    config;
}
exports.AvailableVerification = AvailableVerification;
__decorate([
    (0, class_validator_1.IsEnum)(verification_configuration_1.EVerificationNames),
    (0, swagger_1.ApiProperty)({
        example: `stripe`,
        description: 'The name that identifies the verification method',
    }),
    __metadata("design:type", String)
], AvailableVerification.prototype, "verification", void 0);
__decorate([
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    (0, swagger_1.ApiProperty)({
        example: `{ amount: 50, currency: 'usd', capture: true, noDecimal: false }`,
        description: 'Optional config related to the verification method',
    }),
    __metadata("design:type", Object)
], AvailableVerification.prototype, "config", void 0);
class InitiateOutputDTO {
    availableVerifications;
    requiredFields;
    stateToken;
    nextStep;
    abTests;
}
exports.InitiateOutputDTO = InitiateOutputDTO;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: `[{
      verification: "veratad",
      description: "Description of the veratad method",
    }]`,
        description: 'The list of verification methods',
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => AvailableVerification),
    __metadata("design:type", Array)
], InitiateOutputDTO.prototype, "availableVerifications", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, swagger_1.ApiProperty)({
        example: ``,
        description: 'The required fields when calling the method',
    }),
    __metadata("design:type", Object)
], InitiateOutputDTO.prototype, "requiredFields", void 0);
__decorate([
    (0, class_validator_1.MinLength)(1),
    (0, swagger_1.ApiProperty)({
        example: `base64 encoded string`,
        description: 'The state token to pass to every transaction',
    }),
    __metadata("design:type", String)
], InitiateOutputDTO.prototype, "stateToken", void 0);
__decorate([
    (0, class_validator_1.IsInt)(),
    (0, swagger_1.ApiProperty)({
        example: '1',
        description: 'The nest step to call to continue the verification process',
    }),
    __metadata("design:type", Number)
], InitiateOutputDTO.prototype, "nextStep", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => TVerificationABTestItem),
    (0, swagger_1.ApiProperty)({
        example: `[{
                "testName": "exampleTestName",
                "testGroup": "A",
                "testMethod": "no-method"
              }]`,
        description: 'The list of ABTests applied',
    }),
    __metadata("design:type", Array)
], InitiateOutputDTO.prototype, "abTests", void 0);
class StepOutputDTO {
    verified;
    transactionId;
    errorCode;
    requiredFields;
    stateToken;
    nextStep;
    nextStepData;
    isChargeable;
    verificationSubType;
    isIdentifiedMinor;
}
exports.StepOutputDTO = StepOutputDTO;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: `true`,
        description: 'Returns true or false depending if the user was verified',
    }),
    __metadata("design:type", Boolean)
], StepOutputDTO.prototype, "verified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: ``,
        description: 'The transaction ID of the verification method',
    }),
    __metadata("design:type", String)
], StepOutputDTO.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: `ERR_GENERAL`,
        description: 'An unique error code',
    }),
    __metadata("design:type", String)
], StepOutputDTO.prototype, "errorCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: ``,
        description: 'Schema required for the next step',
    }),
    __metadata("design:type", Object)
], StepOutputDTO.prototype, "requiredFields", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: `base64 encoded string`,
        description: 'The state token to pass to every transaction',
    }),
    __metadata("design:type", String)
], StepOutputDTO.prototype, "stateToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '1',
        description: 'The nest step to call to continue the verification process',
    }),
    __metadata("design:type", Number)
], StepOutputDTO.prototype, "nextStep", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: `{ paymentIntentId: '123', status: 'requires_action', clientSecret: 'secret'}`,
        description: 'The data necessary to perform actions related to next step in the client',
    }),
    __metadata("design:type", Object)
], StepOutputDTO.prototype, "nextStepData", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: `true`,
        description: 'Determines if the step is chargeable or not',
    }),
    __metadata("design:type", Boolean)
], StepOutputDTO.prototype, "isChargeable", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'debit-card',
        description: 'A verification sub-type, attached to a method.',
    }),
    __metadata("design:type", String)
], StepOutputDTO.prototype, "verificationSubType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'true',
        description: 'Determines if a user verifying is a minor',
    }),
    __metadata("design:type", Boolean)
], StepOutputDTO.prototype, "isIdentifiedMinor", void 0);
class FirstStepInputDTO {
    stateToken;
    selectedVerification;
    verificationPayload;
}
exports.FirstStepInputDTO = FirstStepInputDTO;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: `base64 encoded string`,
        description: 'The state token to pass to every transaction',
    }),
    __metadata("design:type", String)
], FirstStepInputDTO.prototype, "stateToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '"veratad"',
        description: 'Selected verification method for this verification process',
    }),
    __metadata("design:type", String)
], FirstStepInputDTO.prototype, "selectedVerification", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: ``,
        description: 'The payload required by the verification method for the current step',
    }),
    __metadata("design:type", Object)
], FirstStepInputDTO.prototype, "verificationPayload", void 0);
class OtherStepInputDTO {
    stateToken;
    verificationPayload;
    minimumAge;
    maximumAge;
}
exports.OtherStepInputDTO = OtherStepInputDTO;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: `base64 encoded string`,
        description: 'The state token to pass to every transaction',
    }),
    __metadata("design:type", String)
], OtherStepInputDTO.prototype, "stateToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: ``,
        description: 'The payload required by the verification method for the current step',
    }),
    __metadata("design:type", Object)
], OtherStepInputDTO.prototype, "verificationPayload", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: `18`,
        description: 'The minimum age defines the upper bound to verify users age',
    }),
    __metadata("design:type", Number)
], OtherStepInputDTO.prototype, "minimumAge", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        example: `17`,
        description: 'The maximum age defines the upper bound to verify users age',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], OtherStepInputDTO.prototype, "maximumAge", void 0);
//# sourceMappingURL=verifications.dto.js.map