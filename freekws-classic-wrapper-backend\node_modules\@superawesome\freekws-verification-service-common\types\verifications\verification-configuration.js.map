{"version": 3, "file": "verification-configuration.js", "sourceRoot": "", "sources": ["../../../types/verifications/verification-configuration.ts"], "names": [], "mappings": ";;;AAAA,IAAY,kBAUX;AAVD,WAAY,kBAAkB;IAC5B,sCAAgB,CAAA;IAChB,wCAAkB,CAAA;IAClB,sCAAgB,CAAA;IAChB,4CAAsB,CAAA;IACtB,wCAAkB,CAAA;IAClB,kDAA4B,CAAA;IAC5B,4CAAsB,CAAA;IACtB,wCAAkB,CAAA;IAClB,8CAAwB,CAAA;AAC1B,CAAC,EAVW,kBAAkB,kCAAlB,kBAAkB,QAU7B;AAED,IAAY,qBAGX;AAHD,WAAY,qBAAqB;IAC/B,iDAAwB,CAAA;IACxB,mDAA0B,CAAA;AAC5B,CAAC,EAHW,qBAAqB,qCAArB,qBAAqB,QAGhC;AAED,IAAY,kBAYX;AAZD,WAAY,kBAAkB;IAC5B,yCAAmB,CAAA;IACnB,oDAA8B,CAAA;IAC9B,uCAAiB,CAAA;IACjB,2CAAqB,CAAA;IACrB,uCAAiB,CAAA;IACjB,mCAAa,CAAA;IACb,uCAAiB,CAAA;IACjB,8CAAwB,CAAA;IACxB,8CAAwB,CAAA;IACxB,0CAAoB,CAAA;IACpB,wDAAkC,CAAA;AACpC,CAAC,EAZW,kBAAkB,kCAAlB,kBAAkB,QAY7B;AAQD,IAAY,iBAeX;AAfD,WAAY,iBAAiB;IAC3B,wCAAmB,CAAA;IACnB,mDAA8B,CAAA;IAC9B,sCAAiB,CAAA;IACjB,0CAAqB,CAAA;IACrB,sCAAiB,CAAA;IACjB,kCAAa,CAAA;IACb,sCAAiB,CAAA;IACjB,6CAAwB,CAAA;IACxB,yCAAoB,CAAA;IACpB,6CAAwB,CAAA;IACxB,wEAAmD,CAAA;IACnD,8DAAyC,CAAA;IACzC,2CAAsB,CAAA;AAExB,CAAC,EAfW,iBAAiB,iCAAjB,iBAAiB,QAe5B;AAGD,IAAY,yBAEX;AAFD,WAAY,yBAAyB;IACnC,8CAAiB,CAAA;AACnB,CAAC,EAFW,yBAAyB,yCAAzB,yBAAyB,QAEpC;AAeM,MAAM,qBAAqB,GAAG,CAAC,IAAwB,EAAkC,EAAE;IAChG,OAAO,qCAA6B,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;AACnG,CAAC,CAAC;AAFW,QAAA,qBAAqB,yBAEhC;AAEK,MAAM,qBAAqB,GAAG,CAAC,IAAwB,EAAkC,EAAE;IAChG,OAAO,qCAA6B,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;AAChG,CAAC,CAAC;AAFW,QAAA,qBAAqB,yBAEhC;AAEK,MAAM,yBAAyB,GAAG,GAAyB,EAAE,CAClE,qCAA6B,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;AADnD,QAAA,yBAAyB,6BAC0B;AAEzD,MAAM,6BAA6B,GAAG,CAAC,IAAwB,EAAwB,EAAE,CAC9F,qCAA6B,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;AADlF,QAAA,6BAA6B,iCACqD;AAElF,QAAA,gCAAgC,GAA0B;IACrE;QACE,IAAI,EAAE,kBAAkB,CAAC,OAAO;QAChC,oBAAoB,EAAE,KAAK;KAC5B;IACD;QACE,IAAI,EAAE,kBAAkB,CAAC,SAAS;QAClC,oBAAoB,EAAE,KAAK;KAC5B;IACD;QACE,IAAI,EAAE,kBAAkB,CAAC,QAAQ;QACjC,oBAAoB,EAAE,KAAK;KAC5B;IACD;QACE,IAAI,EAAE,kBAAkB,CAAC,MAAM;QAC/B,oBAAoB,EAAE,KAAK;KAC5B;IACD;QACE,IAAI,EAAE,kBAAkB,CAAC,MAAM;QAC/B,oBAAoB,EAAE,KAAK;KAC5B;IACD;QACE,IAAI,EAAE,kBAAkB,CAAC,YAAY;QACrC,oBAAoB,EAAE,KAAK;KAC5B;IACD;QACE,IAAI,EAAE,kBAAkB,CAAC,MAAM;QAC/B,oBAAoB,EAAE,KAAK;KAC5B;IACD;QACE,IAAI,EAAE,kBAAkB,CAAC,IAAI;QAC7B,oBAAoB,EAAE,KAAK;KAC5B;IACD;QACE,IAAI,EAAE,kBAAkB,CAAC,SAAS;QAClC,oBAAoB,EAAE,KAAK;KAC5B;IACD;QACE,IAAI,EAAE,kBAAkB,CAAC,OAAO;QAChC,oBAAoB,EAAE,KAAK;KAC5B;IACD;QACE,IAAI,EAAE,kBAAkB,CAAC,cAAc;QACvC,oBAAoB,EAAE,KAAK;KAC5B;CACF,CAAC;AAEW,QAAA,6BAA6B,GAA8B;IACtE;QACE,KAAK,EAAE,wBAAwB;QAC/B,WAAW,EAAE,oGAAoG;QACjH,IAAI,EAAE,kBAAkB,CAAC,KAAK;QAC9B,aAAa,EAAE,CAAC,kBAAkB,CAAC,SAAS,EAAE,kBAAkB,CAAC,OAAO,CAAC;QACzE,kBAAkB,EAAE,KAAK;KAC1B;IACD;QACE,KAAK,EAAE,SAAS;QAChB,WAAW,EAAE,mDAAmD;QAChE,IAAI,EAAE,kBAAkB,CAAC,MAAM;QAC/B,aAAa,EAAE,CAAC,kBAAkB,CAAC,YAAY,EAAE,kBAAkB,CAAC,MAAM,CAAC;QAC3E,kBAAkB,EAAE,KAAK;KAC1B;IACD;QACE,KAAK,EAAE,aAAa;QACpB,WAAW,EAAE,kGAAkG;QAC/G,IAAI,EAAE,kBAAkB,CAAC,MAAM;QAC/B,aAAa,EAAE,CAAC,kBAAkB,CAAC,QAAQ,CAAC;QAC5C,kBAAkB,EAAE,KAAK;KAC1B;IACD;QACE,KAAK,EAAE,sBAAsB;QAC7B,WAAW,EACT,4FAA4F;YAC5F,sGAAsG;YACtG,gDAAgD;QAClD,IAAI,EAAE,kBAAkB,CAAC,WAAW;QACpC,aAAa,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;QAC1C,kBAAkB,EAAE,KAAK;KAC1B;IACD;QACE,KAAK,EAAE,YAAY;QACnB,WAAW,EAAE,iGAAiG;QAC9G,IAAI,EAAE,kBAAkB,CAAC,KAAK;QAC9B,aAAa,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;QAC1C,kBAAkB,EAAE,KAAK;KAC1B;IACD;QACE,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,qDAAqD;QAClE,IAAI,EAAE,kBAAkB,CAAC,QAAQ;QACjC,aAAa,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC;QACxC,kBAAkB,EAAE,KAAK;KAC1B;IACD;QACE,KAAK,EAAE,0BAA0B;QACjC,WAAW,EAAE,6FAA6F;QAC1G,IAAI,EAAE,kBAAkB,CAAC,QAAQ;QACjC,aAAa,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC;QAC7C,kBAAkB,EAAE,KAAK;KAC1B;IACD;QACE,KAAK,EAAE,sBAAsB;QAC7B,WAAW,EAAE,gGAAgG;QAC7G,IAAI,EAAE,kBAAkB,CAAC,MAAM;QAC/B,aAAa,EAAE,CAAC,kBAAkB,CAAC,OAAO,CAAC;QAC3C,kBAAkB,EAAE,KAAK;KAC1B;IACD;QACE,KAAK,EAAE,SAAS;QAChB,WAAW,EAAE,4FAA4F;QACzG,IAAI,EAAE,kBAAkB,CAAC,SAAS;QAClC,aAAa,EAAE,CAAC,kBAAkB,CAAC,cAAc,CAAC;QAClD,kBAAkB,EAAE,KAAK;KAC1B;CACF,CAAC;AAEW,QAAA,gCAAgC,GAAiE;IAC5G,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,qBAAqB,CAAC,UAAU,EAAE,qBAAqB,CAAC,SAAS,CAAC;CACjG,CAAC"}