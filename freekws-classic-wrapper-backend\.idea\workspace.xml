<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fdabc46e-482e-4b10-aa13-d617b0f2e911" name="Changes" comment="Making settings service test cleaner">
      <change beforePath="$PROJECT_DIR$/src/oauth/oauth.service.spec.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/oauth/oauth.service.spec.ts" afterDir="false" />
    </list>
    <list id="d9df4e59-f9f5-47bb-9bff-7a41343549ee" name="Ignore" comment="Update keycloak and allow you to login locally for debugging">
      <change beforePath="$PROJECT_DIR$/.husky/pre-commit" beforeDir="false" afterPath="$PROJECT_DIR$/.husky/pre-commit" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="package.json" />
        <option value="JavaScript File" />
        <option value="HTTP Private Environment File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <favorite-branches>
      <branch-storage>
        <map>
          <entry type="LOCAL">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$" source="dom-dms-branch" />
                <branch-info repo="$PROJECT_DIR$" source="kcmp-396-active-user-endpoint-for-non-niantic-customers-endpoint" />
                <branch-info repo="$PROJECT_DIR$" source="kcmp-399-innersloth-fix-more-golden-masters" />
                <branch-info repo="$PROJECT_DIR$" source="golden-master-fixes-3" />
                <branch-info repo="$PROJECT_DIR$" source="kcmp-355-load-testing" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </favorite-branches>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {}
}</component>
  <component name="GitHubPullRequestState">{
  &quot;prStates&quot;: [
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOM5J4mM6K8Bk2&quot;,
        &quot;number&quot;: 40
      },
      &quot;lastSeen&quot;: 1739784888673
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOM5J4mM6aDTtW&quot;,
        &quot;number&quot;: 102
      },
      &quot;lastSeen&quot;: 1749727333205
    }
  ]
}</component>
  <component name="GitRewordedCommitMessages">
    <option name="commitMessagesMapping">
      <RewordedCommitMessageMapping>
        <option name="originalMessage" value="WIP" />
        <option name="rewordedMessage" value="Implement dob bypass" />
      </RewordedCommitMessageMapping>
    </option>
    <option name="currentCommit" value="1" />
    <option name="onto" value="b4f8287422a384a2040bd0d876ecf3171fdf1b8d" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="test-clean-up" />
                    <option name="lastUsedInstant" value="1755318444" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1755318411" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="feat/fetch-parent-email-if-not-provided-in-request-permissions" />
                    <option name="lastUsedInstant" value="1755263441" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="fix/KCMP-464-fix-audit-requestid" />
                    <option name="lastUsedInstant" value="1755249702" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="kcmp-446-add-in-400-bad-request-propagation-for-age-gate" />
                    <option name="lastUsedInstant" value="**********" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/SuperAwesomeLTD/freekws-classic-wrapper-backend&quot;,
    &quot;accountId&quot;: &quot;79deff4f-bacb-4956-a746-1eb078bba3e4&quot;
  },
  &quot;recentNewPullRequestHead&quot;: {
    &quot;server&quot;: {
      &quot;useHttp&quot;: false,
      &quot;host&quot;: &quot;github.com&quot;,
      &quot;port&quot;: null,
      &quot;suffix&quot;: null
    },
    &quot;owner&quot;: &quot;SuperAwesomeLTD&quot;,
    &quot;repository&quot;: &quot;freekws-classic-wrapper-backend&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/node_modules/@superawesome/freekws-settings-common/types/settings/setting.types.d.ts" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/node_modules/@types/jest/index.d.ts" root0="SKIP_INSPECTION" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;configuredContexts&quot;: [
    {
      &quot;name&quot;: &quot;PRODUCTION_kws_innersloth_ap-southeast-1_0&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;PRODUCTION_kws_innersloth_eu-west-1_0&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;PRODUCTION_kws_innersloth_us-east-1_0&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;PRODUCTION_kws_main_eu-west-1_1&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;PRODUCTION_kws_niantic_ap-southeast-1_1&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;PRODUCTION_kws_niantic_eu-west-1_1&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;PRODUCTION_kws_niantic_us-east-1_1&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;PRODUCTION_kws_reporting_eu-west-1_0&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;PRODUCTION_kws_v2_eu-west-1_0&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;PRODUCTION_kws_v2_us-east-1_0&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;staging_kws_main_eu-west-1_1&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;staging_kws_reporting_eu-west-1_0&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;staging_kws_v2_eu-west-1_0&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;staging_kws_v2_us-east-1_0&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    },
    {
      &quot;name&quot;: &quot;staging_kws_v2_us-east-1_1&quot;,
      &quot;kubeConfigUrl&quot;: &quot;file://C:/Users/<USER>/.kube/config&quot;
    }
  ],
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2rZPenvegAGhnqsMQB8fwEIkp7p" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Attach to Node.js/Chrome.Attach NestJS Debugger.executor": "Debug",
    "Attach to Node.js/Chrome.Debug E2E Tests.executor": "Debug",
    "Cucumber.js.users-get.feature.executor": "Run",
    "HTTP Request.req | 20 JWK Endpoint.executor": "Run",
    "HTTP Request.req | Check Username Wrapper -- Non-local.executor": "Run",
    "HTTP Request.req | Check Username Wrapper.executor": "Run",
    "HTTP Request.req | Check Username with Badger.executor": "Run",
    "HTTP Request.test | #1.executor": "Run",
    "HTTP Request.test | #4.executor": "Run",
    "HTTP Request.test | #5.executor": "Run",
    "HTTP Request.test | #6.executor": "Run",
    "HTTP Request.test | #7.executor": "Run",
    "HTTP Request.test | #8.executor": "Run",
    "HTTP Request.test | OAuth.executor": "Run",
    "HTTP Request.test | Something Else.executor": "Run",
    "HTTP Request.test_prod | #4.executor": "Run",
    "HTTP Request.test_prod | #5.executor": "Run",
    "Jest.AgeGateService.executor": "Run",
    "Jest.AgeGateService.getConsentAgeForCountry.should transform age gate invalid country 400 error to BadRequestException.executor": "Debug",
    "Jest.All Tests in common > jest.config.js.executor": "Run",
    "Jest.All Tests.executor": "Run",
    "Jest.AmplitudeService.executor": "Run",
    "Jest.AnalyticService.activationSuccess.executor": "Run",
    "Jest.AppController (e2e).DELETE /v2/apps/:appId/users/:userId.deletes user's settings.executor": "Run",
    "Jest.AppController (e2e).DELETE /v2/apps/:appId/users/:userId.executor": "Run",
    "Jest.AppController (e2e).POST /v2/apps/:appId/users.should return expected response.executor": "Run",
    "Jest.AppController (e2e).executor": "Run",
    "Jest.AppController.activeUserToAnotherApp.executor": "Run",
    "Jest.AppController.deleteUserSettings.executor": "Run",
    "Jest.AppController.deleteUserSettings.throws NotFound when user has no activation.executor": "Run",
    "Jest.AppController.executor": "Run",
    "Jest.AppController.getUser.executor": "Run",
    "Jest.AppController.updateUserPermissions.executor": "Run",
    "Jest.AppController.updateUserPermissions.should attempt to activate user for Innersloth org env.executor": "Run",
    "Jest.AppController.updateUserPermissions.should handle empty permissions by returning user data.executor": "Run",
    "Jest.AppController.updateUserPermissions.should throw NotFoundException when user is not activated for provided app and permissions are provided.executor": "Run",
    "Jest.AppRepository.deleteUserSettings.deletes user settings.executor": "Run",
    "Jest.AppRepository.deleteUserSettings.executor": "Run",
    "Jest.AppRepository.executor": "Run",
    "Jest.AppRepository.requestPermissionsForUser.executor": "Run",
    "Jest.AppRepository.requestPermissionsForUser.should add T&C permission when termsAndConditionsRequired is true and user has not accepted T&C.executor": "Run",
    "Jest.AppRepository.userHasActivation.executor": "Run",
    "Jest.AppService.executor": "Run",
    "Jest.AppService.getAppConfig.should handle Niantic org env ID for identity name.executor": "Run",
    "Jest.AppService.getAppConfig.should handle Pokemon Go app with special background color.executor": "Run",
    "Jest.AppService.getAppConfig.should return app config with translations.executor": "Run",
    "Jest.AppService.getAppOauthClient.should return oauth client params.executor": "Run",
    "Jest.AppService.getTranslatedPermissionsForApp.executor": "Run",
    "Jest.AppService.getTranslatedPermissionsForApp.should remove terms and conditions settings from results.executor": "Run",
    "Jest.AppService.registerUser.should create major user without consent email.executor": "Run",
    "Jest.AppService.requestPermissionsForUser.should not add T&C permission when termsAndConditionsRequired is true but user has already accepted T&C.executor": "Debug",
    "Jest.AppService.reviewPermissions.executor": "Run",
    "Jest.AppsUsersController.getUserPermissions.executor": "Run",
    "Jest.AppsUsersController.getUserPermissions.should return extended permissions.executor": "Run",
    "Jest.AppsUsersController.should be defined.executor": "Run",
    "Jest.BadgerService.executor": "Run",
    "Jest.BadgerService.moderateUsername.should handle different language codes correctly.executor": "Run",
    "Jest.CallbackService.executor": "Run",
    "Jest.ClientCredentialsGuard.returns true when getting org env from request.executor": "Run",
    "Jest.ConfigService.executor": "Run",
    "Jest.DeleteOldRefreshTokens.executor": "Run",
    "Jest.DeleteOldRefreshTokens.in production mode.should delete tokens in batches.executor": "Run",
    "Jest.EventsController.eventsV2.executor": "Run",
    "Jest.ExceptionsFilter.executor": "Run",
    "Jest.ExceptionsFilter.getErrorMessage.executor": "Run",
    "Jest.ExceptionsFilter.getErrorMessage.returns detail for BadRequestException with string response.executor": "Debug",
    "Jest.ExceptionsFilter.getStatusCode returns status from axios error response.executor": "Run",
    "Jest.ExceptionsFilter.should process axios error correctly.executor": "Run",
    "Jest.FamilyGroupService.executor": "Run",
    "Jest.InjectClientOauthGuard.executor": "Run",
    "Jest.InjectClientOauthGuard.should return true for valid params.executor": "Run",
    "Jest.InjectClientOauthGuard.should throw an error for request with invalid client credentials.executor": "Debug",
    "Jest.InternalAdminController (e2e).GET /internal-admin/users.executor": "Run",
    "Jest.InternalAdminController.deleteUserAccount.executor": "Run",
    "Jest.InternalAdminService.deleteUserAccount.executor": "Run",
    "Jest.JWKService.executor": "Run",
    "Jest.JwkController.executor": "Run",
    "Jest.JwkRepository.executor": "Run",
    "Jest.KeycloakService.executor": "Run",
    "Jest.KwsSignatureService.executor": "Run",
    "Jest.OAuth DTO's.lenient scope transform.should allow missing scope without setting default.executor": "Run",
    "Jest.OAuthTokenHandlers.executor": "Run",
    "Jest.OauthController.authorise.executor": "Run",
    "Jest.OauthController.executor": "Run",
    "Jest.OauthController.token.executor": "Run",
    "Jest.OauthController.token.should return valid response.executor": "Run",
    "Jest.OauthController.token.token.executor": "Run",
    "Jest.OauthController.token.token.should generate new access and refresh tokens when valid refresh_token is provided.executor": "Run",
    "Jest.OauthGuard.executor": "Run",
    "Jest.OauthService.createUserToken.should handle missing parent email.executor": "Run",
    "Jest.OauthService.executor": "Run",
    "Jest.OauthService.getAccessToken.should throw ForbiddenException when appId is not provided.executor": "Run",
    "Jest.OauthService.getAccessToken.should throw an error for invalid scope.executor": "Run",
    "Jest.OauthService.getAccessTokenFromRefreshToken.should create non-user token when refresh token is not for a user.executor": "Run",
    "Jest.OauthService.getAccessTokenFromRefreshToken.should create user token when refresh token has valid userId.executor": "Run",
    "Jest.OrgEnvRepository.executor": "Run",
    "Jest.PolicyGuard.canActivate() with classes.executor": "Run",
    "Jest.PolicyGuard.canActivate() with classes.should return true for no handlers.executor": "Debug",
    "Jest.SettingsService.deleteUser.executor": "Run",
    "Jest.SettingsService.deleteUserSettingsAtProductLevel.executor": "Run",
    "Jest.SettingsService.executor": "Run",
    "Jest.SettingsService.resendConsentEmail.executor": "Run",
    "Jest.SettingsService.resendConsentEmail.should throw an error if consent not requested.executor": "Debug",
    "Jest.SettingsService.resendConsentEmail.should throw an error if parent not a part of user family.executor": "Debug",
    "Jest.SettingsService.sendConsentEmail.should include existing non-irrevocable settings that are not in the initial request.executor": "Run",
    "Jest.SettingsService.sendConsentEmail.should not duplicate settings that are already in the request.executor": "Run",
    "Jest.SettingsService.transformSettingsToAllPermissions.executor": "Run",
    "Jest.SettingsService.transformSettingsToAllPermissions.maps permission value to null when value not set by parent and effective value is false.executor": "Run",
    "Jest.TalonService.executor": "Run",
    "Jest.TalonService.verify.executor": "Run",
    "Jest.UserController.activateUser.executor": "Run",
    "Jest.UserController.activateUser.should activate user to app when app ID matches.executor": "Run",
    "Jest.UserController.activateUser.should activate user when token belongs to requested app.executor": "Debug",
    "Jest.UserController.activateUser.should throw ForbiddenException when appId is missing from JWT.executor": "Debug",
    "Jest.UserController.checkUsername.executor": "Run",
    "Jest.UserController.deleteUserAccount.deletes user account when no password is provided.executor": "Run",
    "Jest.UserController.deleteUserAccount.executor": "Run",
    "Jest.UserController.executor": "Run",
    "Jest.UserController.registerUser.executor": "Run",
    "Jest.UserService.activateUserToApp.activates user to app with permissions.executor": "Run",
    "Jest.UserService.activateUserToApp.executor": "Run",
    "Jest.UserService.createUserV2.executor": "Run",
    "Jest.UserService.createUserV2.should activate user for the origin app when provided.executor": "Run",
    "Jest.UserService.createUserV2.should create a user successfully.executor": "Run",
    "Jest.UserService.createUserV2.should create a user without password when password is not provided.executor": "Run",
    "Jest.UserService.createUserV2.should throw BadRequestException when date of birth is invalid.executor": "Run",
    "Jest.UserService.createUserV2.should throw BadRequestException when user is not a minor.executor": "Run",
    "Jest.UserService.deleteActivation.Deletes activation.executor": "Run",
    "Jest.UserService.deleteActivation.executor": "Run",
    "Jest.UserService.executor": "Run",
    "Jest.UserService.isUsernameAvailable.executor": "Run",
    "Jest.UserService.searchUsersByUsername.executor": "Run",
    "Jest.UserService.userActivatedForApp.executor": "Run",
    "Jest.UserService.verifyPassword.executor": "Run",
    "Jest.UsersController (e2e).GET /v1/users/:userId/apps.grants user activation222.executor": "Run",
    "Jest.WebhookController.executor": "Run",
    "Jest.WebhookGuard.executor": "Run",
    "Jest.WebhookGuard.should throw exception when signature is invalid.executor": "Run",
    "Jest.WebhookService.executor": "Run",
    "Jest.WebhookService.sendSettingsEffectiveValuesChanged.executor": "Run",
    "Jest.WebhookService.sendSettingsEffectiveValuesChanged.should return early if webhook is null.executor": "Run",
    "Jest.WebhookService.sendSettingsEffectiveValuesChanged.should send expected dto to kafka.executor": "Debug",
    "Jest.WebhookService.sendSettingsEffectiveValuesChanged.should use SettingsService.classicValue when setting has a definition property.executor": "Run",
    "Jest.WebhookService.sendSettingsEffectiveValuesChanged.should use SettingsService.classicValue when setting has definition property.executor": "Run",
    "Jest.filterTermsAndConditions (1).executor": "Run",
    "Jest.filterTermsAndConditions.executor": "Run",
    "Jest.isAxiosError.executor": "Run",
    "Jest.oauth.service.spec.ts.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SONARLINT_PRECOMMIT_ANALYSIS": "false",
    "Shell Script.Docker Compose Down.executor": "Run",
    "Shell Script.Wait for Debugger.executor": "Debug",
    "Shell Script.Wait for Keycloak.executor": "Debug",
    "Shell Script.deleteRefreshTokenJob.executor": "Run",
    "Shell Script.docs:generate.executor": "Run",
    "Shell Script.docs:generate:debug (1).executor": "Run",
    "Shell Script.docs:generate:debug.executor": "Run",
    "Shell Script.migration:generate.executor": "Run",
    "Shell Script.migration:generate:local.executor": "Run",
    "Shell Script.migration:run.executor": "Run",
    "Shell Script.start.executor": "Run",
    "Shell Script.start:dev.executor": "Debug",
    "Shell Script.test.executor": "Run",
    "Shell Script.test:acceptance.executor": "Run",
    "Shell Script.test:e2e --no-clean.executor": "Run",
    "Shell Script.test:e2e UAT.executor": "Run",
    "Shell Script.test:e2e.executor": "Run",
    "Shell Script.test:e2e:debug.executor": "Debug",
    "Shell Script.test:e2e:muted-logs --no-clean .executor": "Run",
    "Shell Script.test:migration.executor": "Run",
    "database.data.extractors.current.export.id": "SQL Inserts",
    "database.data.extractors.current.id": "SQL Inserts",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "k6.src\\app-users\\apps-users.controller.spec.ts.executor": "Run",
    "last_opened_file_path": "C:/Users/<USER>/.k8s-portforward.json",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs.cucumber.cucumber_node_package_dir": "C:/Users/<USER>/git/Codurance/Epic Games/freekws-classic-wrapper-backend/node_modules/@cucumber/cucumber",
    "nodejs.jest.jest_package": "C:/Users/<USER>/git/Codurance/EpicGames/freekws-classic-wrapper-backend/node_modules/jest",
    "nodejs_package_manager_path": "npm",
    "npm.build.executor": "Run",
    "npm.docs:generate.executor": "Run",
    "npm.golden-master.executor": "Run",
    "npm.golden-master:generate-input.executor": "Run",
    "npm.golden-master:run-no-input.executor": "Run",
    "npm.golden-master:run-with-input.executor": "Run",
    "npm.k6 > build.executor": "Run",
    "npm.k6 > build2.executor": "Run",
    "npm.k6 > build:sequential-scenarios.executor": "Run",
    "npm.k6 > build:webhook.executor": "Run",
    "npm.k6 > load-test-2.executor": "Run",
    "npm.k6 > load-test.executor": "Run",
    "npm.k6 > load:get-user.executor": "Run",
    "npm.k6 > test:all.executor": "Run",
    "npm.k6 > test:sequential-scenarios.executor": "Run",
    "npm.k6 > test:sequential.executor": "Run",
    "npm.k6 > test:users.executor": "Run",
    "npm.k6 > test:webhooks.executor": "Run",
    "npm.lint.executor": "Run",
    "npm.lint:fix.executor": "Run",
    "npm.lint:fix:ignore-line-endings.executor": "Run",
    "npm.lint:fix:pr.executor": "Run",
    "npm.lint:ignore-formatting.executor": "Run",
    "npm.lint:ignore-line-endings.executor": "Run",
    "npm.lint:staged.executor": "Run",
    "npm.migration:generate.executor": "Run",
    "npm.migration:generate:local.executor": "Run",
    "npm.migration:run.executor": "Run",
    "npm.migration:run:local.executor": "Run",
    "npm.migration:show:local.executor": "Run",
    "npm.node > classic-query.executor": "Debug",
    "npm.node > get-secrets.executor": "Run",
    "npm.node-scripts > convert-to-msgpack.executor": "Debug",
    "npm.node-scripts > convert-to-sqlite.executor": "Run",
    "npm.node-scripts > count-missing-ids.executor": "Run",
    "npm.node-scripts > extract-classic-ids.executor": "Debug",
    "npm.node-scripts > innersloth:compare-activation-ids-sqlite.executor": "Run",
    "npm.node-scripts > innersloth:compare-user-ids-sqlite.executor": "Run",
    "npm.node-scripts > innersloth:extract-classic-activation-ids.executor": "Run",
    "npm.node-scripts > innersloth:extract-classic-user-ids.executor": "Run",
    "npm.node-scripts > innersloth:extract-wrapper-activation-ids.executor": "Run",
    "npm.node-scripts > innersloth:extract-wrapper-user-ids.executor": "Run",
    "npm.node-scripts > innersloth:touch-missing-activation-ids.executor": "Run",
    "npm.node-scripts > innersloth:touch-missing-ids.executor": "Run",
    "npm.node-scripts > innersloth:touch-missing-user-ids-from-db.executor": "Run",
    "npm.node-scripts > innersloth:touch-missing-user-ids.executor": "Run",
    "npm.node-scripts > innersloth:verify-missing-activation-ids.executor": "Run",
    "npm.node-scripts > innersloth:verify-missing-user-ids.executor": "Run",
    "npm.node-scripts > niantic-classic:convert-to-msgpack.executor": "Run",
    "npm.node-scripts > niantic-wrapper:convert-to-msgpack.executor": "Run",
    "npm.node-scripts > niantic:compare-activation-ids-sqlite.executor": "Run",
    "npm.node-scripts > niantic:compare-user-ids-sqlite.executor": "Run",
    "npm.node-scripts > niantic:compare-user-ids.executor": "Run",
    "npm.node-scripts > niantic:extract-classic-activation-ids.executor": "Run",
    "npm.node-scripts > niantic:extract-classic-ids.executor": "Run",
    "npm.node-scripts > niantic:extract-classic-user-ids.executor": "Run",
    "npm.node-scripts > niantic:extract-wrapper-activation-ids.executor": "Run",
    "npm.node-scripts > niantic:extract-wrapper-user-ids.executor": "Run",
    "npm.node-scripts > niantic:touch-missing-activation-ids.executor": "Run",
    "npm.node-scripts > niantic:touch-missing-ids.executor": "Run",
    "npm.node-scripts > niantic:touch-missing-user-ids-from-db.executor": "Run",
    "npm.node-scripts > niantic:touch-missing-user-ids.executor": "Run",
    "npm.node-scripts > niantic:verify-missing-activation-ids.executor": "Run",
    "npm.node-scripts > niantic:verify-missing-user-ids.executor": "Run",
    "npm.node-scripts > shared:compare-activation-ids.executor": "Run",
    "npm.node-scripts > shared:compare-user-ids.executor": "Run",
    "npm.node-scripts > shared:touch-missing-classic-activation-ids.executor": "Debug",
    "npm.node-scripts > shared:touch-missing-classic-user-ids.executor": "Debug",
    "npm.node-scripts > staging-classic:convert-to-msgpack.executor": "Run",
    "npm.node-scripts > staging-wrapper:convert-to-msgpack.executor": "Run",
    "npm.node-scripts > staging-wrapper:extract-wrapper-user-ids.executor": "Debug",
    "npm.node-scripts > staging:extract-classic-ids.executor": "Run",
    "npm.node-scripts > test.executor": "Run",
    "npm.packages:build.executor": "Run",
    "npm.start.executor": "Run",
    "npm.start:debug.executor": "Debug",
    "npm.start:debug:local.executor": "Debug",
    "npm.start:dev.executor": "Run",
    "npm.start:local.executor": "Run",
    "npm.test.executor": "Run",
    "npm.test:cov.executor": "Run",
    "npm.test:e2e.executor": "Run",
    "npm.test:e2e:local.executor": "Run",
    "npm.test:e2e:no-wait.executor": "Run",
    "npm.test:golden.executor": "Run",
    "npm.test:migration:local.executor": "Run",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\node_modules\\typescript\\lib",
    "vcs.patch.to.clipboard": "true",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql_aurora_aws",
      "postgresql"
    ],
    "com.intellij.ide.scratch.LRUPopupBuilder$1/SQL Dialect": [
      "SQLite"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\.dev" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\dms-scripts\node-scripts\scripts\sharedCustomers" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\dms-scripts\node-scripts" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\migration" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\dms-scripts\node-scripts\scripts\sharedCustomers" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\src\common" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\src\common\types" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\dms-scripts\node-scripts\data" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\dms-scripts\node-scripts\db" />
    </key>
    <key name="es6.move.members.recent.items">
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\test\utils\index.ts" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\src\utils.ts" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\dms-scripts\node-scripts\scripts\get-json-from-file.ts" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\dms-scripts\node-scripts\scripts\fileUtils.ts" />
      <recent name="C:\Users\<USER>\git\Codurance\EpicGames\freekws-classic-wrapper-backend\dms-scripts\node-scripts\scripts\constants.ts" />
    </key>
  </component>
  <component name="RunManager" selected="Jest.SettingsService">
    <configuration name="Attach NestJS Debugger" type="ChromiumRemoteDebugType" factoryName="Chromium Remote" activateToolWindowBeforeRun="false" port="9229" restartOnDisconnect="true">
      <method v="2" />
    </configuration>
    <configuration name="Debug E2E Tests" type="ChromiumRemoteDebugType" factoryName="Chromium Remote" port="9229">
      <method v="2">
        <option name="RunConfigurationTask" enabled="true" run_configuration_name="Wait for Debugger" run_configuration_type="ShConfigurationType" />
      </method>
    </configuration>
    <configuration name="Debug E2E Tests" type="CompoundRunConfigurationType">
      <toRun name="Debug E2E Tests" type="ChromiumRemoteDebugType" />
      <toRun name="test:e2e:debug" type="ShConfigurationType" />
      <method v="2" />
    </configuration>
    <configuration name="All Tests" type="JavaScriptTestRunnerJest" nameIsGenerated="true">
      <node-interpreter value="project" />
      <jest-package value="$PROJECT_DIR$/node_modules/jest" />
      <working-dir value="$PROJECT_DIR$" />
      <jest-options value="--maxWorkers=50%" />
      <envs>
        <env name="NODE_ENV" value="test" />
      </envs>
      <scope-kind value="ALL" />
      <method v="2" />
    </configuration>
    <configuration name="SettingsService" type="JavaScriptTestRunnerJest" temporary="true" nameIsGenerated="true">
      <node-interpreter value="project" />
      <jest-package value="$PROJECT_DIR$/node_modules/jest" />
      <working-dir value="$PROJECT_DIR$" />
      <jest-options value="--maxWorkers=50%" />
      <envs />
      <scope-kind value="SUITE" />
      <test-file value="$PROJECT_DIR$/src/common/services/settings/settings.service.spec.ts" />
      <test-names>
        <test-name value="SettingsService" />
      </test-names>
      <method v="2" />
    </configuration>
    <configuration default="true" type="JavaScriptTestRunnerJest">
      <node-interpreter value="project" />
      <jest-options value="--maxWorkers=50%" />
      <envs />
      <scope-kind value="ALL" />
      <method v="2" />
    </configuration>
    <configuration name="Docker Compose Down" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="docker-compose --project-name freekws-classic-wrapper-backend down  " />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="" />
      <option name="SCRIPT_OPTIONS" value="" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="false" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="Wait for Debugger" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/wait-for-debugger.sh" />
      <option name="SCRIPT_OPTIONS" value="" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="Wait for Keycloak" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/wait-for-it.sh" />
      <option name="SCRIPT_OPTIONS" value="-t 120 localhost:18080" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="deleteRefreshTokenJob" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="job DeleteOldRefreshTokens" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="docs:generate" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="docs:generate --no-clean" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="docs:generate:debug" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="docs:generate:debug" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="docs:generate:no-build" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="docs:generate:no-build" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="migration:generate" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="migration:generate" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="migration:generate:local" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="migration:generate:local" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="migration:run" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="migration:run" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="start" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="start" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="start:dev" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="start:dev" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="test" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test:acceptance" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-uat-run.sh" />
      <option name="SCRIPT_OPTIONS" value="test:acceptance" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test:e2e --no-clean" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="test:e2e --no-clean" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test:e2e UAT" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-uat-run.sh" />
      <option name="SCRIPT_OPTIONS" value="test:e2e" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test:e2e" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="test:e2e" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test:e2e:debug" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="test:e2e:debug --no-clean" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test:e2e:muted-logs --no-clean " type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="test:e2e:muted-logs --no-clean" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test:migration" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/docker-npm-run.sh" />
      <option name="SCRIPT_OPTIONS" value="test:migration" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="false" />
      <option name="INTERPRETER_PATH" value="$PROJECT_DIR$/../../../../../../Program Files/Git/bin/bash.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="golden-master:run-no-input" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="golden-master:run-no-input" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="k6 &gt; load-test" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/k6/package.json" />
      <command value="run" />
      <scripts>
        <script value="load-test" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="k6 &gt; test:sequential-scenarios" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/k6/package.json" />
      <command value="run" />
      <scripts>
        <script value="test:sequential-scenarios" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="lint:fix" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="lint:fix" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="lint:ignore-formatting" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="lint:ignore-formatting" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="lint:staged" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="lint:staged" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="migration:generate" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="migration:generate" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="node-scripts &gt; innersloth:touch-missing-user-ids-from-db" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/dms-scripts/node-scripts/package.json" />
      <command value="run" />
      <scripts>
        <script value="innersloth:touch-missing-user-ids-from-db" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="node-scripts &gt; niantic:touch-missing-user-ids-from-db" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/dms-scripts/node-scripts/package.json" />
      <command value="run" />
      <scripts>
        <script value="niantic:touch-missing-user-ids-from-db" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="packages:build" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="packages:build" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="start:debug:local" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="start:debug:local" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="start:dev" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="start:dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="test" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test:e2e" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="test:e2e" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test:e2e:local" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="test:e2e:local" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Attach to Node.js/Chrome.Attach NestJS Debugger" />
      <item itemvalue="Attach to Node.js/Chrome.Debug E2E Tests" />
      <item itemvalue="Compound.Debug E2E Tests" />
      <item itemvalue="Jest.All Tests" />
      <item itemvalue="Jest.SettingsService" />
      <item itemvalue="npm.k6 &gt; test:sequential-scenarios" />
      <item itemvalue="npm.node-scripts &gt; innersloth:touch-missing-user-ids-from-db" />
      <item itemvalue="npm.node-scripts &gt; niantic:touch-missing-user-ids-from-db" />
      <item itemvalue="npm.golden-master:run-no-input" />
      <item itemvalue="npm.packages:build" />
      <item itemvalue="npm.lint:staged" />
      <item itemvalue="npm.lint:fix" />
      <item itemvalue="npm.start:dev" />
      <item itemvalue="npm.test:e2e:local" />
      <item itemvalue="npm.lint:ignore-formatting" />
      <item itemvalue="npm.start:debug:local" />
      <item itemvalue="npm.k6 &gt; load-test" />
      <item itemvalue="npm.migration:generate" />
      <item itemvalue="npm.test" />
      <item itemvalue="npm.test:e2e" />
      <item itemvalue="Shell Script.Docker Compose Down" />
      <item itemvalue="Shell Script.docs:generate" />
      <item itemvalue="Shell Script.docs:generate:debug" />
      <item itemvalue="Shell Script.docs:generate:no-build" />
      <item itemvalue="Shell Script.Wait for Debugger" />
      <item itemvalue="Shell Script.Wait for Keycloak" />
      <item itemvalue="Shell Script.start" />
      <item itemvalue="Shell Script.test:acceptance" />
      <item itemvalue="Shell Script.start:dev" />
      <item itemvalue="Shell Script.test" />
      <item itemvalue="Shell Script.test:migration" />
      <item itemvalue="Shell Script.deleteRefreshTokenJob" />
      <item itemvalue="Shell Script.migration:generate" />
      <item itemvalue="Shell Script.migration:generate:local" />
      <item itemvalue="Shell Script.migration:run" />
      <item itemvalue="Shell Script.test:e2e" />
      <item itemvalue="Shell Script.test:e2e --no-clean" />
      <item itemvalue="Shell Script.test:e2e:muted-logs --no-clean " />
      <item itemvalue="Shell Script.test:e2e UAT" />
      <item itemvalue="Shell Script.test:e2e:debug" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Jest.SettingsService" />
        <item itemvalue="npm.test:e2e" />
        <item itemvalue="npm.test" />
        <item itemvalue="npm.migration:generate" />
        <item itemvalue="npm.k6 &gt; load-test" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-76f8388c3a79-JavaScript-WS-243.24978.60" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fdabc46e-482e-4b10-aa13-d617b0f2e911" name="Changes" comment="" />
      <changelist id="d9df4e59-f9f5-47bb-9bff-7a41343549ee" name="Ignore" comment="" />
      <created>1736763722778</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1736763722778</updated>
      <workItem from="1736763724417" duration="18135000" />
      <workItem from="1736817145893" duration="21685000" />
      <workItem from="1736931269321" duration="29447000" />
      <workItem from="1737030594789" duration="20769000" />
      <workItem from="1737079079052" duration="27241000" />
      <workItem from="1737305526373" duration="9401000" />
      <workItem from="1737366019436" duration="51694000" />
      <workItem from="1737563156836" duration="29159000" />
      <workItem from="1737734141737" duration="15137000" />
      <workItem from="1738055058698" duration="20334000" />
      <workItem from="1738076066489" duration="2762000" />
      <workItem from="1738079776510" duration="27661000" />
      <workItem from="1738149044778" duration="33741000" />
      <workItem from="1738246920178" duration="27756000" />
      <workItem from="1738322592942" duration="27782000" />
      <workItem from="1738582082122" duration="2248000" />
      <workItem from="1738747364642" duration="14664000" />
      <workItem from="1738800784763" duration="6193000" />
      <workItem from="1738857312985" duration="1518000" />
      <workItem from="1738872384561" duration="1374000" />
      <workItem from="1739185864793" duration="29483000" />
      <workItem from="1739282518564" duration="49000" />
      <workItem from="1739282581307" duration="35839000" />
      <workItem from="1739350923179" duration="32807000" />
      <workItem from="1739453915356" duration="12480000" />
      <workItem from="1739527732269" duration="4618000" />
      <workItem from="1739784848863" duration="341000" />
      <workItem from="1739804934666" duration="445000" />
      <workItem from="1739908261424" duration="8348000" />
      <workItem from="1740404408446" duration="784000" />
      <workItem from="1740474896743" duration="2709000" />
      <workItem from="1740485770991" duration="330000" />
      <workItem from="1740651529839" duration="4496000" />
      <workItem from="1740661185980" duration="118000" />
      <workItem from="1740661323036" duration="4344000" />
      <workItem from="1740665902138" duration="37000" />
      <workItem from="1740665947292" duration="775000" />
      <workItem from="1740666742033" duration="281000" />
      <workItem from="1740667221987" duration="55000" />
      <workItem from="1740667284576" duration="205000" />
      <workItem from="1740667497650" duration="127000" />
      <workItem from="1740667632450" duration="2623000" />
      <workItem from="1740670265444" duration="83186000" />
      <workItem from="1741118336950" duration="81803000" />
      <workItem from="1741570724093" duration="51803000" />
      <workItem from="1741711886039" duration="998000" />
      <workItem from="1741740144409" duration="12443000" />
      <workItem from="1741788805361" duration="551251000" />
      <workItem from="1742461239717" duration="39783000" />
      <workItem from="1742762368249" duration="114599000" />
      <workItem from="1743428109361" duration="156394000" />
      <workItem from="1744013010603" duration="604000" />
      <workItem from="1744020203134" duration="8550000" />
      <workItem from="1744186301504" duration="45015000" />
      <workItem from="1744618942320" duration="81904000" />
      <workItem from="1744790506246" duration="62760000" />
      <workItem from="1745890148863" duration="50147000" />
      <workItem from="1746108143104" duration="1534000" />
      <workItem from="1746109698020" duration="134000" />
      <workItem from="1746109841278" duration="208000" />
      <workItem from="1746110068976" duration="25292000" />
      <workItem from="1746201762874" duration="66725000" />
      <workItem from="1746777740938" duration="22366000" />
      <workItem from="1747098930166" duration="28365000" />
      <workItem from="1747226477635" duration="10638000" />
      <workItem from="1747298272060" duration="102723000" />
      <workItem from="1747990485644" duration="105493000" />
      <workItem from="1748822015866" duration="69701000" />
      <workItem from="1749134666544" duration="74890000" />
      <workItem from="1749628926348" duration="21948000" />
      <workItem from="1749715458747" duration="15078000" />
      <workItem from="1749738098083" duration="59391000" />
      <workItem from="1750194913573" duration="152525000" />
      <workItem from="1751272681872" duration="58612000" />
      <workItem from="1752482625692" duration="95553000" />
      <workItem from="1753085024444" duration="31361000" />
      <workItem from="1753325604563" duration="598000" />
      <workItem from="1754049256963" duration="39666000" />
      <workItem from="1754468216777" duration="30819000" />
      <workItem from="1754640944039" duration="42628000" />
      <workItem from="1755043401786" duration="51159000" />
      <workItem from="1755249389290" duration="14590000" />
    </task>
    <task id="LOCAL-00828" summary="Add in local migration test">
      <option name="closed" value="true" />
      <created>1755075641622</created>
      <option name="number" value="00828" />
      <option name="presentableId" value="LOCAL-00828" />
      <option name="project" value="LOCAL" />
      <updated>1755075641622</updated>
    </task>
    <task id="LOCAL-00829" summary="Linting">
      <option name="closed" value="true" />
      <created>1755075712884</created>
      <option name="number" value="00829" />
      <option name="presentableId" value="LOCAL-00829" />
      <option name="project" value="LOCAL" />
      <updated>1755075712884</updated>
    </task>
    <task id="LOCAL-00830" summary="Add in cache to ESLint">
      <option name="closed" value="true" />
      <created>1755075956281</created>
      <option name="number" value="00830" />
      <option name="presentableId" value="LOCAL-00830" />
      <option name="project" value="LOCAL" />
      <updated>1755075956281</updated>
    </task>
    <task id="LOCAL-00831" summary="Remove ArrayUnique">
      <option name="closed" value="true" />
      <created>1755081527587</created>
      <option name="number" value="00831" />
      <option name="presentableId" value="LOCAL-00831" />
      <option name="project" value="LOCAL" />
      <updated>1755081527587</updated>
    </task>
    <task id="LOCAL-00832" summary="Make healthcheck accept wild cards">
      <option name="closed" value="true" />
      <created>1755082929718</created>
      <option name="number" value="00832" />
      <option name="presentableId" value="LOCAL-00832" />
      <option name="project" value="LOCAL" />
      <updated>1755082929718</updated>
    </task>
    <task id="LOCAL-00833" summary="Linting">
      <option name="closed" value="true" />
      <created>1755083043740</created>
      <option name="number" value="00833" />
      <option name="presentableId" value="LOCAL-00833" />
      <option name="project" value="LOCAL" />
      <updated>1755083043740</updated>
    </task>
    <task id="LOCAL-00834" summary="Give permissions an array check to ensure it is actually an array">
      <option name="closed" value="true" />
      <created>1755084539066</created>
      <option name="number" value="00834" />
      <option name="presentableId" value="LOCAL-00834" />
      <option name="project" value="LOCAL" />
      <updated>1755084539066</updated>
    </task>
    <task id="LOCAL-00835" summary="Make pipeline faster">
      <option name="closed" value="true" />
      <created>1755084771261</created>
      <option name="number" value="00835" />
      <option name="presentableId" value="LOCAL-00835" />
      <option name="project" value="LOCAL" />
      <updated>1755084771261</updated>
    </task>
    <task id="LOCAL-00836" summary="Add in array validation">
      <option name="closed" value="true" />
      <created>1755085307605</created>
      <option name="number" value="00836" />
      <option name="presentableId" value="LOCAL-00836" />
      <option name="project" value="LOCAL" />
      <updated>1755085307605</updated>
    </task>
    <task id="LOCAL-00837" summary="Add in logs for further debugging">
      <option name="closed" value="true" />
      <created>1755085355794</created>
      <option name="number" value="00837" />
      <option name="presentableId" value="LOCAL-00837" />
      <option name="project" value="LOCAL" />
      <updated>1755085355794</updated>
    </task>
    <task id="LOCAL-00838" summary="Formatting">
      <option name="closed" value="true" />
      <created>1755085631487</created>
      <option name="number" value="00838" />
      <option name="presentableId" value="LOCAL-00838" />
      <option name="project" value="LOCAL" />
      <updated>1755085631487</updated>
    </task>
    <task id="LOCAL-00839" summary="Remvoe isArray">
      <option name="closed" value="true" />
      <created>1755090838129</created>
      <option name="number" value="00839" />
      <option name="presentableId" value="LOCAL-00839" />
      <option name="project" value="LOCAL" />
      <updated>1755090838130</updated>
    </task>
    <task id="LOCAL-00840" summary="Add user friendly customer name and metric">
      <option name="closed" value="true" />
      <created>1755095476132</created>
      <option name="number" value="00840" />
      <option name="presentableId" value="LOCAL-00840" />
      <option name="project" value="LOCAL" />
      <updated>1755095476132</updated>
    </task>
    <task id="LOCAL-00841" summary="Comment out isArray">
      <option name="closed" value="true" />
      <created>1755095486282</created>
      <option name="number" value="00841" />
      <option name="presentableId" value="LOCAL-00841" />
      <option name="project" value="LOCAL" />
      <updated>1755095486282</updated>
    </task>
    <task id="LOCAL-00842" summary="Linting">
      <option name="closed" value="true" />
      <created>1755096119142</created>
      <option name="number" value="00842" />
      <option name="presentableId" value="LOCAL-00842" />
      <option name="project" value="LOCAL" />
      <updated>1755096119142</updated>
    </task>
    <task id="LOCAL-00843" summary="Add in more detailed log for missing permissions">
      <option name="closed" value="true" />
      <created>1755098121767</created>
      <option name="number" value="00843" />
      <option name="presentableId" value="LOCAL-00843" />
      <option name="project" value="LOCAL" />
      <updated>1755098121767</updated>
    </task>
    <task id="LOCAL-00844" summary="Add in more detailed log for missing permissions">
      <option name="closed" value="true" />
      <created>1755098224178</created>
      <option name="number" value="00844" />
      <option name="presentableId" value="LOCAL-00844" />
      <option name="project" value="LOCAL" />
      <updated>1755098224178</updated>
    </task>
    <task id="LOCAL-00845" summary="Fix E2E test">
      <option name="closed" value="true" />
      <created>1755098768294</created>
      <option name="number" value="00845" />
      <option name="presentableId" value="LOCAL-00845" />
      <option name="project" value="LOCAL" />
      <updated>1755098768294</updated>
    </task>
    <task id="LOCAL-00846" summary="Delete user with orgEnvId">
      <option name="closed" value="true" />
      <created>1755161021506</created>
      <option name="number" value="00846" />
      <option name="presentableId" value="LOCAL-00846" />
      <option name="project" value="LOCAL" />
      <updated>1755161021506</updated>
    </task>
    <task id="LOCAL-00847" summary="Allow permissions to be a JSON stringified array to cope with classic weirdness">
      <option name="closed" value="true" />
      <created>1755165223892</created>
      <option name="number" value="00847" />
      <option name="presentableId" value="LOCAL-00847" />
      <option name="project" value="LOCAL" />
      <updated>1755165223892</updated>
    </task>
    <task id="LOCAL-00848" summary="WIP">
      <option name="closed" value="true" />
      <created>1755167892982</created>
      <option name="number" value="00848" />
      <option name="presentableId" value="LOCAL-00848" />
      <option name="project" value="LOCAL" />
      <updated>1755167892983</updated>
    </task>
    <task id="LOCAL-00849" summary="Add in location to get user settings from resendConsentEmail">
      <option name="closed" value="true" />
      <created>1755168124717</created>
      <option name="number" value="00849" />
      <option name="presentableId" value="LOCAL-00849" />
      <option name="project" value="LOCAL" />
      <updated>1755168124717</updated>
    </task>
    <task id="LOCAL-00850" summary="Linting">
      <option name="closed" value="true" />
      <created>1755168191327</created>
      <option name="number" value="00850" />
      <option name="presentableId" value="LOCAL-00850" />
      <option name="project" value="LOCAL" />
      <updated>1755168191328</updated>
    </task>
    <task id="LOCAL-00851" summary="Make Axios errors actually propagate error codes correctly">
      <option name="closed" value="true" />
      <created>1755170387349</created>
      <option name="number" value="00851" />
      <option name="presentableId" value="LOCAL-00851" />
      <option name="project" value="LOCAL" />
      <updated>1755170387349</updated>
    </task>
    <task id="LOCAL-00852" summary="Remove comments">
      <option name="closed" value="true" />
      <created>1755170418243</created>
      <option name="number" value="00852" />
      <option name="presentableId" value="LOCAL-00852" />
      <option name="project" value="LOCAL" />
      <updated>1755170418243</updated>
    </task>
    <task id="LOCAL-00853" summary="Linting">
      <option name="closed" value="true" />
      <created>1755170498999</created>
      <option name="number" value="00853" />
      <option name="presentableId" value="LOCAL-00853" />
      <option name="project" value="LOCAL" />
      <updated>1755170498999</updated>
    </task>
    <task id="LOCAL-00854" summary="Add in test #coverage">
      <option name="closed" value="true" />
      <created>1755171031341</created>
      <option name="number" value="00854" />
      <option name="presentableId" value="LOCAL-00854" />
      <option name="project" value="LOCAL" />
      <updated>1755171031341</updated>
    </task>
    <task id="LOCAL-00855" summary="Add in route in more circumstances">
      <option name="closed" value="true" />
      <created>1755173130360</created>
      <option name="number" value="00855" />
      <option name="presentableId" value="LOCAL-00855" />
      <option name="project" value="LOCAL" />
      <updated>1755173130360</updated>
    </task>
    <task id="LOCAL-00856" summary="Linting">
      <option name="closed" value="true" />
      <created>1755173200490</created>
      <option name="number" value="00856" />
      <option name="presentableId" value="LOCAL-00856" />
      <option name="project" value="LOCAL" />
      <updated>1755173200490</updated>
    </task>
    <task id="LOCAL-00857" summary="Remove rogue getUserSettings call with the actual one">
      <option name="closed" value="true" />
      <created>1755174137571</created>
      <option name="number" value="00857" />
      <option name="presentableId" value="LOCAL-00857" />
      <option name="project" value="LOCAL" />
      <updated>1755174137571</updated>
    </task>
    <task id="LOCAL-00858" summary="Fix test">
      <option name="closed" value="true" />
      <created>1755179972787</created>
      <option name="number" value="00858" />
      <option name="presentableId" value="LOCAL-00858" />
      <option name="project" value="LOCAL" />
      <updated>1755179972787</updated>
    </task>
    <task id="LOCAL-00859" summary="Fix tests">
      <option name="closed" value="true" />
      <created>1755181051873</created>
      <option name="number" value="00859" />
      <option name="presentableId" value="LOCAL-00859" />
      <option name="project" value="LOCAL" />
      <updated>1755181051873</updated>
    </task>
    <task id="LOCAL-00860" summary="Add in extra log">
      <option name="closed" value="true" />
      <created>1755183724442</created>
      <option name="number" value="00860" />
      <option name="presentableId" value="LOCAL-00860" />
      <option name="project" value="LOCAL" />
      <updated>1755183724442</updated>
    </task>
    <task id="LOCAL-00861" summary="Add quotes">
      <option name="closed" value="true" />
      <created>1755183822507</created>
      <option name="number" value="00861" />
      <option name="presentableId" value="LOCAL-00861" />
      <option name="project" value="LOCAL" />
      <updated>1755183822507</updated>
    </task>
    <task id="LOCAL-00862" summary="Make location always return the default country code for non-truthy values">
      <option name="closed" value="true" />
      <created>1755185937489</created>
      <option name="number" value="00862" />
      <option name="presentableId" value="LOCAL-00862" />
      <option name="project" value="LOCAL" />
      <updated>1755185937489</updated>
    </task>
    <task id="LOCAL-00863" summary="Fix test">
      <option name="closed" value="true" />
      <created>1755186025327</created>
      <option name="number" value="00863" />
      <option name="presentableId" value="LOCAL-00863" />
      <option name="project" value="LOCAL" />
      <updated>1755186025327</updated>
    </task>
    <task id="LOCAL-00864" summary="Linting">
      <option name="closed" value="true" />
      <created>1755186235406</created>
      <option name="number" value="00864" />
      <option name="presentableId" value="LOCAL-00864" />
      <option name="project" value="LOCAL" />
      <updated>1755186235406</updated>
    </task>
    <task id="LOCAL-00865" summary="Remove all ternary logic from sending location for sendConsentEmailAtPRoductLevel">
      <option name="closed" value="true" />
      <created>1755186735335</created>
      <option name="number" value="00865" />
      <option name="presentableId" value="LOCAL-00865" />
      <option name="project" value="LOCAL" />
      <updated>1755186735335</updated>
    </task>
    <task id="LOCAL-00866" summary="Fix tests">
      <option name="closed" value="true" />
      <created>1755186819579</created>
      <option name="number" value="00866" />
      <option name="presentableId" value="LOCAL-00866" />
      <option name="project" value="LOCAL" />
      <updated>1755186819579</updated>
    </task>
    <task id="LOCAL-00867" summary="Linting">
      <option name="closed" value="true" />
      <created>1755187244854</created>
      <option name="number" value="00867" />
      <option name="presentableId" value="LOCAL-00867" />
      <option name="project" value="LOCAL" />
      <updated>1755187244854</updated>
    </task>
    <task id="LOCAL-00868" summary="Typing fix">
      <option name="closed" value="true" />
      <created>1755187752607</created>
      <option name="number" value="00868" />
      <option name="presentableId" value="LOCAL-00868" />
      <option name="project" value="LOCAL" />
      <updated>1755187752608</updated>
    </task>
    <task id="LOCAL-00869" summary="Rename class">
      <option name="closed" value="true" />
      <created>1755251473125</created>
      <option name="number" value="00869" />
      <option name="presentableId" value="LOCAL-00869" />
      <option name="project" value="LOCAL" />
      <updated>1755251473126</updated>
    </task>
    <task id="LOCAL-00870" summary="Add orgEnvId filtering">
      <option name="closed" value="true" />
      <created>1755264194014</created>
      <option name="number" value="00870" />
      <option name="presentableId" value="LOCAL-00870" />
      <option name="project" value="LOCAL" />
      <updated>1755264194014</updated>
    </task>
    <task id="LOCAL-00871" summary="Make change in the other function">
      <option name="closed" value="true" />
      <created>1755264706943</created>
      <option name="number" value="00871" />
      <option name="presentableId" value="LOCAL-00871" />
      <option name="project" value="LOCAL" />
      <updated>1755264706943</updated>
    </task>
    <task id="LOCAL-00872" summary="Fix e2e maybe?">
      <option name="closed" value="true" />
      <created>1755264777722</created>
      <option name="number" value="00872" />
      <option name="presentableId" value="LOCAL-00872" />
      <option name="project" value="LOCAL" />
      <updated>1755264777722</updated>
    </task>
    <task id="LOCAL-00873" summary="Fix the rest of e2e tests">
      <option name="closed" value="true" />
      <created>1755265226236</created>
      <option name="number" value="00873" />
      <option name="presentableId" value="LOCAL-00873" />
      <option name="project" value="LOCAL" />
      <updated>1755265226236</updated>
    </task>
    <task id="LOCAL-00874" summary="Linting">
      <option name="closed" value="true" />
      <created>1755265292008</created>
      <option name="number" value="00874" />
      <option name="presentableId" value="LOCAL-00874" />
      <option name="project" value="LOCAL" />
      <updated>1755265292008</updated>
    </task>
    <task id="LOCAL-00875" summary="Add tests to cover guardian requests">
      <option name="closed" value="true" />
      <created>1755267272013</created>
      <option name="number" value="00875" />
      <option name="presentableId" value="LOCAL-00875" />
      <option name="project" value="LOCAL" />
      <updated>1755267272013</updated>
    </task>
    <task id="LOCAL-00876" summary="Making settings service test cleaner">
      <option name="closed" value="true" />
      <created>1755318590358</created>
      <option name="number" value="00876" />
      <option name="presentableId" value="LOCAL-00876" />
      <option name="project" value="LOCAL" />
      <updated>1755318590358</updated>
    </task>
    <option name="localTasksCounter" value="877" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="includedJSSourcePackages">
      <set>
        <option value="@nestjs/common" />
        <option value="@nestjs/core" />
        <option value="@superawesome/freekws-agegate-api-common" />
        <option value="@superawesome/freekws-auth-library" />
        <option value="@superawesome/freekws-cache-decorator" />
        <option value="@superawesome/freekws-clients-nestjs" />
        <option value="@superawesome/freekws-common-http-client" />
        <option value="@superawesome/freekws-devportal-common" />
        <option value="@superawesome/freekws-family-service-common" />
        <option value="@superawesome/freekws-http-nestjs-service" />
        <option value="@superawesome/freekws-kafka" />
        <option value="@superawesome/freekws-metrics" />
        <option value="@superawesome/freekws-metrics-nestjs-service" />
        <option value="@superawesome/freekws-nestjs-guards" />
        <option value="@superawesome/freekws-regional-config" />
        <option value="@superawesome/freekws-settings-common" />
        <option value="@superawesome/freekws-test-keycloak" />
        <option value="axios" />
        <option value="class-validator" />
        <option value="jest-diff" />
        <option value="nock" />
        <option value="typeorm-fixtures-cli" />
      </set>
    </option>
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="javascript:npm:prettier" />
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="executable:az" />
    <option featureType="com.intellij.configurationType" implementationName="K6ConfigurationType" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="HEAD" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="main" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <MESSAGE value="WIP" />
    <MESSAGE value="Add in location to get user settings from resendConsentEmail" />
    <MESSAGE value="Make Axios errors actually propagate error codes correctly" />
    <MESSAGE value="Remove comments" />
    <MESSAGE value="Add in test #coverage" />
    <MESSAGE value="Add in route in more circumstances" />
    <MESSAGE value="Remove rogue getUserSettings call with the actual one" />
    <MESSAGE value="Add in extra log" />
    <MESSAGE value="Add quotes" />
    <MESSAGE value="Make location always return the default country code for non-truthy values" />
    <MESSAGE value="Fix test" />
    <MESSAGE value="Remove all ternary logic from sending location for sendConsentEmailAtPRoductLevel" />
    <MESSAGE value="Fix tests" />
    <MESSAGE value="Typing fix" />
    <MESSAGE value="Update keycloak and allow you to login locally for debugging" />
    <MESSAGE value="Rename class" />
    <MESSAGE value="Line endings" />
    <MESSAGE value="Add orgEnvId filtering" />
    <MESSAGE value="Make change in the other function" />
    <MESSAGE value="Fix e2e maybe?" />
    <MESSAGE value="Fix the rest of e2e tests" />
    <MESSAGE value="Linting" />
    <MESSAGE value="Add tests to cover guardian requests" />
    <MESSAGE value="An attempt" />
    <MESSAGE value="Making settings service test cleaner" />
    <option name="LAST_COMMIT_MESSAGE" value="Making settings service test cleaner" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>