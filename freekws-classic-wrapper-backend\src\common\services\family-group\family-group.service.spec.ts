import { TestingModule } from '@nestjs/testing';
import { createClientMock } from '@superawesome/freekws-clients-base/src/test-utils/mock';
import { FAMILY_SERVICE_CLIENT_INJECT_KEY, familyServicePlugin } from '@superawesome/freekws-family-service-common';

import { FamilyGroupService } from './family-group.service';
import { Testing } from '../../utils';
import { KEYCLOAK_PROVIDER } from '../keycloak/keycloak.module';

const mockFamilyApi = createClientMock(familyServicePlugin, jest.fn);

describe('FamilyGroupService', () => {
  let service: FamilyGroupService;
  const token = 'TOKEN';
  const keycloakMock = { getUpToDateServiceAccessToken: jest.fn().mockResolvedValue(token) };

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [
        FamilyGroupService,
        { provide: FAMILY_SERVICE_CLIENT_INJECT_KEY, useValue: mockFamilyApi },
        { provide: KEYCLOAK_PROVIDER, useValue: keycloakMock },
      ],
    });

    service = module.get<FamilyGroupService>(FamilyGroupService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('getGuardianLinks filters by orgEnvId and sends Authorization header', async () => {
    const orgEnvId = 'env-1';
    const userId = 123;

    mockFamilyApi
      .getModule('internalAdminFamilyGroup')
      .findGuardianLinks.mockResolvedValueOnce({
        data: {
          response: {
            pendingRequests: [
              { orgEnvId: 'env-1' },
              { orgEnvId: 'env-2' },
            ],
            confirmedRequests: [
              { orgEnvId: 'env-1' },
              { orgEnvId: 'env-3' },
            ],
          },
          meta: { requestId: 'id', timestamp: new Date().toISOString() },
        },
        status: 200,
      } as any);

    const result = await service.getGuardianLinks(orgEnvId, userId);

    expect(mockFamilyApi.getModule('internalAdminFamilyGroup').findGuardianLinks).toHaveBeenCalledWith(
      {
        query: { userId: `${userId}` },
      },
      {
        headers: { Authorization: `Bearer ${token}` },
      },
    );

    expect(result.pendingRequests).toEqual([{ orgEnvId: 'env-1' }]);
    expect(result.confirmedRequests).toEqual([{ orgEnvId: 'env-1' }]);
  });

  it('getGuardianLinksFromEmail filters by orgEnvId and uses email query', async () => {
    const orgEnvId = 'env-9';
    const email = '<EMAIL>';

    mockFamilyApi
      .getModule('internalAdminFamilyGroup')
      .findGuardianLinks.mockResolvedValueOnce({
        data: {
          response: {
            pendingRequests: [
              { orgEnvId: 'env-8' },
              { orgEnvId: 'env-9' },
            ],
            confirmedRequests: [
              { orgEnvId: 'env-9' },
              { orgEnvId: 'env-7' },
            ],
          },
          meta: { requestId: 'id', timestamp: new Date().toISOString() },
        },
        status: 200,
      } as any);

    const result = await service.getGuardianLinksFromEmail(orgEnvId, email);

    expect(mockFamilyApi.getModule('internalAdminFamilyGroup').findGuardianLinks).toHaveBeenCalledWith(
      {
        query: { email },
      },
      {
        headers: { Authorization: `Bearer ${token}` },
      },
    );

    expect(result.pendingRequests).toEqual([{ orgEnvId: 'env-9' }]);
    expect(result.confirmedRequests).toEqual([{ orgEnvId: 'env-9' }]);
  });
});
