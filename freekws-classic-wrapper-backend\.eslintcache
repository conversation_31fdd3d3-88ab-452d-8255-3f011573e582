[{"C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\amplitude\\amplitude.module.ts": "1", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\amplitude\\amplitude.service.spec.ts": "2", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\amplitude\\amplitude.service.ts": "3", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app-translation.entity.ts": "4", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.controller.spec.ts": "5", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.controller.ts": "6", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.dto.ts": "7", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.entity.ts": "8", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.module.ts": "9", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.service.spec.ts": "10", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.service.ts": "11", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\oauth-callback-url.entity.ts": "12", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\types.ts": "13", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\bootstrap.spec.ts": "14", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\bootstrap.ts": "15", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\common.module.ts": "16", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\filters\\exceptions\\exceptions.filter.spec.ts": "17", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\filters\\exceptions\\exceptions.filter.ts": "18", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\filters\\exceptions\\index.ts": "19", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-client-oauth\\inject-client-oauth.decorator.ts": "20", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-client-oauth\\inject-client-oauth.guard.spec.ts": "21", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-client-oauth\\inject-client-oauth.guard.ts": "22", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-client-oauth\\types.ts": "23", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-org-env\\inject-org-env.decorator.ts": "24", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-org-env\\inject-org-env.guard.spec.ts": "25", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-org-env\\inject-org-env.guard.ts": "26", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-org-env\\types.ts": "27", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\oauth\\jwt.decorator.ts": "28", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\oauth\\oauth.guard.spec.ts": "29", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\oauth\\oauth.guard.ts": "30", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\oauth\\types.ts": "31", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\app\\own-app.policy.spec.ts": "32", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\app\\own-app.policy.ts": "33", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\boolean-ops\\boolean-ops.policy.spec.ts": "34", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\boolean-ops\\boolean-ops.policy.ts": "35", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\index.ts": "36", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\jwt\\has-scope.policy.spec.ts": "37", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\jwt\\has-scope.policy.ts": "38", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\policies.decorator.ts": "39", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\policies.guard.spec.ts": "40", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\policies.guard.ts": "41", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\types.ts": "42", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\webhook\\types.ts": "43", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\webhook\\webhook.guard.spec.ts": "44", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\webhook\\webhook.guard.ts": "45", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\age-gate\\age-gate.module.ts": "46", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\age-gate\\age-gate.service.spec.ts": "47", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\age-gate\\age-gate.service.ts": "48", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\analytic\\analytic.module.ts": "49", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\analytic\\analytic.service.spec.ts": "50", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\analytic\\analytic.service.ts": "51", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\analytic\\types.ts": "52", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\branding\\branding.module.ts": "53", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\branding\\branding.service.spec.ts": "54", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\branding\\branding.service.ts": "55", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\callback\\callback.module.ts": "56", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\callback\\callback.service.spec.ts": "57", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\callback\\callback.service.ts": "58", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\config\\config.module.ts": "59", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\config\\config.service.spec.ts": "60", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\config\\config.service.ts": "61", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\config\\types.ts": "62", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\email\\email-enqueue.module.ts": "63", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\email\\email-enqueue.service.ts": "64", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\email\\enqueue-email.service.spec.ts": "65", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\email\\types.ts": "66", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\family-group\\family-group.module.ts": "67", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\family-group\\family-group.service.spec.ts": "68", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\family-group\\family-group.service.ts": "69", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\kafka\\kafka-producer.module.ts": "70", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\keycloak\\client-keycloak.service.ts": "71", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\keycloak\\keycloak.module.ts": "72", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\keycloak\\keycloak.service.spec.ts": "73", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\kws-signature\\kws-signature.module.ts": "74", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\kws-signature\\kws-signature.service.spec.ts": "75", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\kws-signature\\kws-signature.service.ts": "76", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\kws-signature\\types.ts": "77", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\pre-verification\\pre-verification.module.ts": "78", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\pre-verification\\pre-verification.service.spec.ts": "79", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\pre-verification\\pre-verification.service.ts": "80", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\pre-verification\\types.ts": "81", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\settings\\settings.module.ts": "82", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\settings\\settings.service.spec.ts": "83", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\settings\\settings.service.ts": "84", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\settings\\types.ts": "85", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\translations\\translations.module.ts": "86", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\translations\\translations.service.spec.ts": "87", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\translations\\translations.service.ts": "88", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\types\\index.ts": "89", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\utils\\index.ts": "90", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\utils\\testing\\index.ts": "91", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\countries\\countries.controller.spec.ts": "92", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\countries\\countries.controller.ts": "93", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\countries\\countries.module.ts": "94", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\countries\\countries.service.spec.ts": "95", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\countries\\countries.service.ts": "96", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\events\\events.controller.spec.ts": "97", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\events\\events.controller.ts": "98", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\events\\events.dto.ts": "99", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\events\\events.module.ts": "100", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.controller.spec.ts": "101", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.controller.ts": "102", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.dto.ts": "103", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.module.ts": "104", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.service.spec.ts": "105", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.service.ts": "106", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\types.ts": "107", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\internal-admin\\internal-admin.controller.spec.ts": "108", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\internal-admin\\internal-admin.controller.ts": "109", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\internal-admin\\internal-admin.module.ts": "110", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\internal-admin\\internal-admin.service.spec.ts": "111", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\internal-admin\\internal-admin.service.ts": "112", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\base-job.spec.ts": "113", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\base-job.ts": "114", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\delete-old-token-refresh-entries\\delete-old-token-refresh-entries.spec.ts": "115", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\delete-old-token-refresh-entries\\delete-old-token-refresh-entries.ts": "116", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\generic-job-runner.spec.ts": "117", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\generic-job-runner.ts": "118", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\job-list.ts": "119", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\run-job.ts": "120", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\main.ts": "121", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.controller.spec.ts": "122", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.controller.ts": "123", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.entity.ts": "124", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.module.ts": "125", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.repository.spec.ts": "126", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.repository.ts": "127", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.service.spec.ts": "128", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.service.ts": "129", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\legacy-jwk.entity.ts": "130", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth-token-handlers.spec.ts": "131", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth-token-handlers.ts": "132", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth.controller.spec.ts": "133", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth.controller.ts": "134", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth.module.ts": "135", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth.service.spec.ts": "136", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth.service.ts": "137", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\refresh-token.entity.ts": "138", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\type-check.ts": "139", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\types.ts": "140", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\org-env\\org-env.entity.ts": "141", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\org-env\\org-env.module.ts": "142", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\org-env\\org-env.service.spec.ts": "143", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\org-env\\org-env.service.ts": "144", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\org-env\\types.ts": "145", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\root-app.module.ts": "146", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\activation.entity.ts": "147", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\badger.module.ts": "148", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\badger.service.spec.ts": "149", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\badger.service.ts": "150", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\talon.module.ts": "151", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\talon.service.spec.ts": "152", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\talon.service.ts": "153", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\types.ts": "154", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.controller.spec.ts": "155", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.controller.ts": "156", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.dto.ts": "157", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.entity.spec.ts": "158", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.entity.ts": "159", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.module.ts": "160", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.service.spec.ts": "161", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.service.ts": "162", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\utils.spec.ts": "163", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\utils.ts": "164", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\types.ts": "165", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.controller.spec.ts": "166", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.controller.ts": "167", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.entity.ts": "168", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.module.ts": "169", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.service.spec.ts": "170", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.service.ts": "171", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\app\\app.controller.e2e-spec.ts": "172", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\countries\\countries.controller.e2e-spec.ts": "173", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\events\\events.controller.e2e-spec.ts": "174", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\healthcheck\\healthcheck.controller.e2e-spec.ts": "175", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\internal-admin\\internal-admin.controller.e2e-spec.ts": "176", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\oauth\\jwk.controller.e2e-spec.ts": "177", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\oauth\\oauth.controller.e2e-spec.ts": "178", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\user\\user.controller.e2e-spec.ts": "179", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\user\\user.entity.e2e-spec.ts": "180", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\utils\\constants.ts": "181", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\utils\\index.ts": "182", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\utils\\kafka-helper.ts": "183", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\utils\\request-helper.ts": "184", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\utils\\test-config.service.ts": "185", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\webhooks\\webhooks.controller.e2e-spec.ts": "186", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\fixtures\\app.fixture.ts": "187", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\fixtures\\oauth-callback-url.fixture.ts": "188", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\fixtures\\org-env.fixture.ts": "189", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\fixtures\\webhook.fixture.ts": "190", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\formatters\\junit-formatter.ts": "191", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\migration\\*************-add-uat-seeds.ts": "192", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\ormconfig.uat.ts": "193", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\app-users.steps.ts": "194", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\child-account-graduated.steps.ts": "195", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\countries.steps.ts": "196", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\families-group-deleted.steps.ts": "197", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\unresponsive-parent-deleted.steps.ts": "198", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\user-added-to-family.steps.ts": "199", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\user-permission-changed.steps.ts": "200", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\user-removed-from-family.steps.ts": "201", "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\utils\\index.ts": "202"}, {"size": 318, "mtime": 1754989235690, "results": "203", "hashOfConfig": "204"}, {"size": 3956, "mtime": 1754989235691, "results": "205", "hashOfConfig": "204"}, {"size": 2777, "mtime": 1754989235692, "results": "206", "hashOfConfig": "204"}, {"size": 1661, "mtime": 1754989235693, "results": "207", "hashOfConfig": "204"}, {"size": 23283, "mtime": 1754994393204, "results": "208", "hashOfConfig": "204"}, {"size": 14711, "mtime": 1755183497297, "results": "209", "hashOfConfig": "204"}, {"size": 2125, "mtime": 1754989235696, "results": "210", "hashOfConfig": "204"}, {"size": 2461, "mtime": 1754994393205, "results": "211", "hashOfConfig": "204"}, {"size": 1191, "mtime": 1754994393206, "results": "212", "hashOfConfig": "204"}, {"size": 52348, "mtime": 1755263441342, "results": "213", "hashOfConfig": "204"}, {"size": 21400, "mtime": 1755263441343, "results": "214", "hashOfConfig": "204"}, {"size": 800, "mtime": 1754994393208, "results": "215", "hashOfConfig": "204"}, {"size": 1836, "mtime": 1754989235700, "results": "216", "hashOfConfig": "204"}, {"size": 2222, "mtime": 1754297878395, "results": "217", "hashOfConfig": "204"}, {"size": 1232, "mtime": 1754297878457, "results": "218", "hashOfConfig": "204"}, {"size": 1862, "mtime": 1754989235701, "results": "219", "hashOfConfig": "204"}, {"size": 6674, "mtime": 1755249819335, "results": "220", "hashOfConfig": "204"}, {"size": 8100, "mtime": 1755249658261, "results": "221", "hashOfConfig": "204"}, {"size": 245, "mtime": 1737117893823, "results": "222", "hashOfConfig": "204"}, {"size": 548, "mtime": 1738314214430, "results": "223", "hashOfConfig": "204"}, {"size": 3008, "mtime": 1755249658263, "results": "224", "hashOfConfig": "204"}, {"size": 3223, "mtime": 1755249658264, "results": "225", "hashOfConfig": "204"}, {"size": 435, "mtime": 1738314214430, "results": "226", "hashOfConfig": "204"}, {"size": 417, "mtime": 1738314214430, "results": "227", "hashOfConfig": "204"}, {"size": 1080, "mtime": 1755249658266, "results": "228", "hashOfConfig": "204"}, {"size": 780, "mtime": 1755249658267, "results": "229", "hashOfConfig": "204"}, {"size": 332, "mtime": 1738314214430, "results": "230", "hashOfConfig": "204"}, {"size": 414, "mtime": 1737117893823, "results": "231", "hashOfConfig": "204"}, {"size": 2906, "mtime": 1739362976563, "results": "232", "hashOfConfig": "204"}, {"size": 1657, "mtime": 1755249658269, "results": "233", "hashOfConfig": "204"}, {"size": 344, "mtime": 1738314214430, "results": "234", "hashOfConfig": "204"}, {"size": 1214, "mtime": 1737117893824, "results": "235", "hashOfConfig": "204"}, {"size": 497, "mtime": 1738341057369, "results": "236", "hashOfConfig": "204"}, {"size": 3428, "mtime": 1738314214430, "results": "237", "hashOfConfig": "204"}, {"size": 2147, "mtime": 1737117893824, "results": "238", "hashOfConfig": "204"}, {"size": 200, "mtime": 1737117893824, "results": "239", "hashOfConfig": "204"}, {"size": 1230, "mtime": 1737117893824, "results": "240", "hashOfConfig": "204"}, {"size": 754, "mtime": 1737117893824, "results": "241", "hashOfConfig": "204"}, {"size": 271, "mtime": 1738314214430, "results": "242", "hashOfConfig": "204"}, {"size": 8793, "mtime": 1749807657444, "results": "243", "hashOfConfig": "204"}, {"size": 2611, "mtime": 1749807657444, "results": "244", "hashOfConfig": "204"}, {"size": 631, "mtime": 1738314214430, "results": "245", "hashOfConfig": "204"}, {"size": 451, "mtime": 1749807657445, "results": "246", "hashOfConfig": "204"}, {"size": 2357, "mtime": 1755249658270, "results": "247", "hashOfConfig": "204"}, {"size": 1335, "mtime": 1755249658271, "results": "248", "hashOfConfig": "204"}, {"size": 2119, "mtime": 1754989235705, "results": "249", "hashOfConfig": "204"}, {"size": 6203, "mtime": 1755249658272, "results": "250", "hashOfConfig": "204"}, {"size": 2993, "mtime": 1755249658273, "results": "251", "hashOfConfig": "204"}, {"size": 384, "mtime": 1737117893824, "results": "252", "hashOfConfig": "204"}, {"size": 1514, "mtime": 1738341057369, "results": "253", "hashOfConfig": "204"}, {"size": 1978, "mtime": 1738341057369, "results": "254", "hashOfConfig": "204"}, {"size": 347, "mtime": 1737117893824, "results": "255", "hashOfConfig": "204"}, {"size": 2386, "mtime": 1754989235708, "results": "256", "hashOfConfig": "204"}, {"size": 7192, "mtime": 1754989235708, "results": "257", "hashOfConfig": "204"}, {"size": 2317, "mtime": 1754989235708, "results": "258", "hashOfConfig": "204"}, {"size": 2134, "mtime": 1755249701587, "results": "259", "hashOfConfig": "204"}, {"size": 2915, "mtime": 1754989235710, "results": "260", "hashOfConfig": "204"}, {"size": 1559, "mtime": 1754989235762, "results": "261", "hashOfConfig": "204"}, {"size": 193, "mtime": 1737117893824, "results": "262", "hashOfConfig": "204"}, {"size": 8103, "mtime": 1754989235774, "results": "263", "hashOfConfig": "204"}, {"size": 11253, "mtime": 1754989235775, "results": "264", "hashOfConfig": "204"}, {"size": 2613, "mtime": 1754989235776, "results": "265", "hashOfConfig": "204"}, {"size": 943, "mtime": 1754989235776, "results": "266", "hashOfConfig": "204"}, {"size": 5988, "mtime": 1754989235777, "results": "267", "hashOfConfig": "204"}, {"size": 12139, "mtime": 1754989235778, "results": "268", "hashOfConfig": "204"}, {"size": 502, "mtime": 1754989235778, "results": "269", "hashOfConfig": "204"}, {"size": 2131, "mtime": 1754989235779, "results": "270", "hashOfConfig": "204"}, {"size": 891, "mtime": 1754989235780, "results": "271", "hashOfConfig": "204"}, {"size": 2313, "mtime": 1755264688117, "results": "272", "hashOfConfig": "204"}, {"size": 945, "mtime": 1738314214431, "results": "273", "hashOfConfig": "204"}, {"size": 1595, "mtime": 1754989235781, "results": "274", "hashOfConfig": "204"}, {"size": 2897, "mtime": 1754989235818, "results": "275", "hashOfConfig": "204"}, {"size": 2403, "mtime": 1754989235819, "results": "276", "hashOfConfig": "204"}, {"size": 326, "mtime": 1749807657456, "results": "277", "hashOfConfig": "204"}, {"size": 3562, "mtime": 1754989235821, "results": "278", "hashOfConfig": "204"}, {"size": 2712, "mtime": 1749807657457, "results": "279", "hashOfConfig": "204"}, {"size": 323, "mtime": 1737117893824, "results": "280", "hashOfConfig": "204"}, {"size": 2153, "mtime": 1754989235822, "results": "281", "hashOfConfig": "204"}, {"size": 4169, "mtime": 1754994393216, "results": "282", "hashOfConfig": "204"}, {"size": 2962, "mtime": 1754989235823, "results": "283", "hashOfConfig": "204"}, {"size": 568, "mtime": 1737117893825, "results": "284", "hashOfConfig": "204"}, {"size": 2361, "mtime": 1754994393217, "results": "285", "hashOfConfig": "204"}, {"size": 76902, "mtime": 1755263441345, "results": "286", "hashOfConfig": "204"}, {"size": 22367, "mtime": 1755249658277, "results": "287", "hashOfConfig": "204"}, {"size": 2463, "mtime": 1755263441346, "results": "288", "hashOfConfig": "204"}, {"size": 328, "mtime": 1754989235830, "results": "289", "hashOfConfig": "204"}, {"size": 3818, "mtime": 1754989235830, "results": "290", "hashOfConfig": "204"}, {"size": 1454, "mtime": 1754989235831, "results": "291", "hashOfConfig": "204"}, {"size": 432, "mtime": 1754989235832, "results": "292", "hashOfConfig": "204"}, {"size": 27, "mtime": 1737117893825, "results": "293", "hashOfConfig": "204"}, {"size": 3276, "mtime": 1738581903246, "results": "294", "hashOfConfig": "204"}, {"size": 2459, "mtime": 1755249658278, "results": "295", "hashOfConfig": "204"}, {"size": 2093, "mtime": 1754989235833, "results": "296", "hashOfConfig": "204"}, {"size": 454, "mtime": 1737117893825, "results": "297", "hashOfConfig": "204"}, {"size": 2086, "mtime": 1738314214431, "results": "298", "hashOfConfig": "204"}, {"size": 933, "mtime": 1738314214431, "results": "299", "hashOfConfig": "204"}, {"size": 6707, "mtime": 1754989235834, "results": "300", "hashOfConfig": "204"}, {"size": 2144, "mtime": 1754989235835, "results": "301", "hashOfConfig": "204"}, {"size": 1979, "mtime": 1754989235835, "results": "302", "hashOfConfig": "204"}, {"size": 386, "mtime": 1754989235836, "results": "303", "hashOfConfig": "204"}, {"size": 1380, "mtime": 1737117893825, "results": "304", "hashOfConfig": "204"}, {"size": 1243, "mtime": 1755249658279, "results": "305", "hashOfConfig": "204"}, {"size": 481, "mtime": 1737117893825, "results": "306", "hashOfConfig": "204"}, {"size": 813, "mtime": 1738314214431, "results": "307", "hashOfConfig": "204"}, {"size": 1792, "mtime": 1738314214431, "results": "308", "hashOfConfig": "204"}, {"size": 2335, "mtime": 1754989235837, "results": "309", "hashOfConfig": "204"}, {"size": 106, "mtime": 1737117893825, "results": "310", "hashOfConfig": "204"}, {"size": 2225, "mtime": 1749807657463, "results": "311", "hashOfConfig": "204"}, {"size": 4060, "mtime": 1754994393220, "results": "312", "hashOfConfig": "204"}, {"size": 549, "mtime": 1738749636386, "results": "313", "hashOfConfig": "204"}, {"size": 3256, "mtime": 1749807657464, "results": "314", "hashOfConfig": "204"}, {"size": 1412, "mtime": 1749807657465, "results": "315", "hashOfConfig": "204"}, {"size": 1075, "mtime": 1754989235839, "results": "316", "hashOfConfig": "317"}, {"size": 431, "mtime": 1754989235839, "results": "318", "hashOfConfig": "317"}, {"size": 3724, "mtime": 1754989235840, "results": "319", "hashOfConfig": "317"}, {"size": 3892, "mtime": 1754989235841, "results": "320", "hashOfConfig": "317"}, {"size": 587, "mtime": 1754989235842, "results": "321", "hashOfConfig": "317"}, {"size": 532, "mtime": 1754989235842, "results": "322", "hashOfConfig": "317"}, {"size": 111, "mtime": 1754989235843, "results": "323", "hashOfConfig": "317"}, {"size": 725, "mtime": 1754989235843, "results": "324", "hashOfConfig": "317"}, {"size": 473, "mtime": 1753800797636, "results": "325", "hashOfConfig": "204"}, {"size": 2662, "mtime": 1749807657469, "results": "326", "hashOfConfig": "204"}, {"size": 731, "mtime": 1749807657469, "results": "327", "hashOfConfig": "204"}, {"size": 886, "mtime": 1749564853361, "results": "328", "hashOfConfig": "204"}, {"size": 516, "mtime": 1755249815482, "results": "329", "hashOfConfig": "204"}, {"size": 1633, "mtime": 1749807657470, "results": "330", "hashOfConfig": "204"}, {"size": 514, "mtime": 1749807657471, "results": "331", "hashOfConfig": "204"}, {"size": 5622, "mtime": 1755249816705, "results": "332", "hashOfConfig": "204"}, {"size": 4083, "mtime": 1755249816010, "results": "333", "hashOfConfig": "204"}, {"size": 574, "mtime": 1755251497060, "results": "334", "hashOfConfig": "204"}, {"size": 13627, "mtime": 1754989235845, "results": "335", "hashOfConfig": "204"}, {"size": 4211, "mtime": 1754989235846, "results": "336", "hashOfConfig": "204"}, {"size": 8588, "mtime": 1749807657472, "results": "337", "hashOfConfig": "204"}, {"size": 4550, "mtime": 1755249658283, "results": "338", "hashOfConfig": "204"}, {"size": 1285, "mtime": 1754989235849, "results": "339", "hashOfConfig": "204"}, {"size": 28563, "mtime": 1755249658284, "results": "340", "hashOfConfig": "204"}, {"size": 11451, "mtime": 1755263441346, "results": "341", "hashOfConfig": "204"}, {"size": 708, "mtime": 1749804821262, "results": "342", "hashOfConfig": "204"}, {"size": 240, "mtime": 1754989235852, "results": "343", "hashOfConfig": "204"}, {"size": 876, "mtime": 1749807657476, "results": "344", "hashOfConfig": "204"}, {"size": 1014, "mtime": 1755249813395, "results": "345", "hashOfConfig": "204"}, {"size": 335, "mtime": 1739361016504, "results": "346", "hashOfConfig": "204"}, {"size": 2640, "mtime": 1739363027881, "results": "347", "hashOfConfig": "204"}, {"size": 1306, "mtime": 1754989235853, "results": "348", "hashOfConfig": "204"}, {"size": 645, "mtime": 1738581903246, "results": "349", "hashOfConfig": "204"}, {"size": 2166, "mtime": 1754989235855, "results": "350", "hashOfConfig": "204"}, {"size": 1138, "mtime": 1749807657477, "results": "351", "hashOfConfig": "204"}, {"size": 510, "mtime": 1749807657478, "results": "352", "hashOfConfig": "204"}, {"size": 5461, "mtime": 1749807657479, "results": "353", "hashOfConfig": "204"}, {"size": 5151, "mtime": 1749807657479, "results": "354", "hashOfConfig": "204"}, {"size": 397, "mtime": 1754989235856, "results": "355", "hashOfConfig": "204"}, {"size": 5424, "mtime": 1754989235857, "results": "356", "hashOfConfig": "204"}, {"size": 2372, "mtime": 1754989235858, "results": "357", "hashOfConfig": "204"}, {"size": 480, "mtime": 1754994393223, "results": "358", "hashOfConfig": "204"}, {"size": 17369, "mtime": 1754994393224, "results": "359", "hashOfConfig": "204"}, {"size": 12622, "mtime": 1755043660826, "results": "360", "hashOfConfig": "204"}, {"size": 6800, "mtime": 1754989235860, "results": "361", "hashOfConfig": "204"}, {"size": 531, "mtime": 1749807657483, "results": "362", "hashOfConfig": "204"}, {"size": 2198, "mtime": 1749807657484, "results": "363", "hashOfConfig": "204"}, {"size": 2200, "mtime": 1754994393225, "results": "364", "hashOfConfig": "204"}, {"size": 31714, "mtime": 1755263441347, "results": "365", "hashOfConfig": "204"}, {"size": 18434, "mtime": 1755263441348, "results": "366", "hashOfConfig": "204"}, {"size": 1530, "mtime": 1754989235864, "results": "367", "hashOfConfig": "204"}, {"size": 3208, "mtime": 1755249658288, "results": "368", "hashOfConfig": "204"}, {"size": 789, "mtime": 1749807657487, "results": "369", "hashOfConfig": "204"}, {"size": 5111, "mtime": 1754989235865, "results": "370", "hashOfConfig": "204"}, {"size": 4250, "mtime": 1755249658290, "results": "371", "hashOfConfig": "204"}, {"size": 1292, "mtime": 1749807657489, "results": "372", "hashOfConfig": "204"}, {"size": 1057, "mtime": 1749807657490, "results": "373", "hashOfConfig": "204"}, {"size": 21507, "mtime": 1754994393229, "results": "374", "hashOfConfig": "204"}, {"size": 13580, "mtime": 1754994393229, "results": "375", "hashOfConfig": "204"}, {"size": 41568, "mtime": 1755263441349, "results": "376", "hashOfConfig": "377"}, {"size": 2012, "mtime": 1755249658292, "results": "378", "hashOfConfig": "377"}, {"size": 3100, "mtime": 1754989235873, "results": "379", "hashOfConfig": "377"}, {"size": 721, "mtime": 1737117893826, "results": "380", "hashOfConfig": "377"}, {"size": 7507, "mtime": 1755265225111, "results": "381", "hashOfConfig": "377"}, {"size": 3496, "mtime": 1749807657499, "results": "382", "hashOfConfig": "377"}, {"size": 11104, "mtime": 1754989235881, "results": "383", "hashOfConfig": "377"}, {"size": 14084, "mtime": 1755251482696, "results": "384", "hashOfConfig": "377"}, {"size": 1359, "mtime": 1737117893826, "results": "385", "hashOfConfig": "377"}, {"size": 8557, "mtime": 1754989235882, "results": "386", "hashOfConfig": "377"}, {"size": 27962, "mtime": 1755265225157, "results": "387", "hashOfConfig": "377"}, {"size": 2923, "mtime": 1739361906029, "results": "388", "hashOfConfig": "377"}, {"size": 1292, "mtime": 1754989235883, "results": "389", "hashOfConfig": "377"}, {"size": 717, "mtime": 1738341057370, "results": "390", "hashOfConfig": "377"}, {"size": 11716, "mtime": 1754989235885, "results": "391", "hashOfConfig": "377"}, {"size": 815, "mtime": 1754994393230, "results": "392", "hashOfConfig": "204"}, {"size": 318, "mtime": 1754994393230, "results": "393", "hashOfConfig": "204"}, {"size": 534, "mtime": 1739287440301, "results": "394", "hashOfConfig": "204"}, {"size": 1183, "mtime": 1752578891300, "results": "395", "hashOfConfig": "204"}, {"size": 8225, "mtime": 1738314214431, "results": "396", "hashOfConfig": "204"}, {"size": 11492, "mtime": 1754994393231, "results": "397", "hashOfConfig": "204"}, {"size": 379, "mtime": 1737117893826, "results": "398", "hashOfConfig": "204"}, {"size": 6416, "mtime": 1749807657493, "results": "399", "hashOfConfig": "204"}, {"size": 2217, "mtime": 1749807657493, "results": "400", "hashOfConfig": "204"}, {"size": 1470, "mtime": 1738314214432, "results": "401", "hashOfConfig": "204"}, {"size": 2879, "mtime": 1749807657494, "results": "402", "hashOfConfig": "204"}, {"size": 2326, "mtime": 1749807657494, "results": "403", "hashOfConfig": "204"}, {"size": 2232, "mtime": 1749807657495, "results": "404", "hashOfConfig": "204"}, {"size": 2513, "mtime": 1749807657495, "results": "405", "hashOfConfig": "204"}, {"size": 2315, "mtime": 1749807657495, "results": "406", "hashOfConfig": "204"}, {"size": 716, "mtime": 1737117893826, "results": "407", "hashOfConfig": "204"}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "m9aojh", {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6l6bhv", {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12e7gj2", {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\amplitude\\amplitude.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\amplitude\\amplitude.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\amplitude\\amplitude.service.ts", [], ["1014"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app-translation.entity.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.controller.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.controller.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.dto.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.entity.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\app.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\oauth-callback-url.entity.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\app\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\bootstrap.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\bootstrap.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\common.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\filters\\exceptions\\exceptions.filter.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\filters\\exceptions\\exceptions.filter.ts", [], ["1015", "1016"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\filters\\exceptions\\index.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-client-oauth\\inject-client-oauth.decorator.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-client-oauth\\inject-client-oauth.guard.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-client-oauth\\inject-client-oauth.guard.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-client-oauth\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-org-env\\inject-org-env.decorator.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-org-env\\inject-org-env.guard.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-org-env\\inject-org-env.guard.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\inject-org-env\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\oauth\\jwt.decorator.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\oauth\\oauth.guard.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\oauth\\oauth.guard.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\oauth\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\app\\own-app.policy.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\app\\own-app.policy.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\boolean-ops\\boolean-ops.policy.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\boolean-ops\\boolean-ops.policy.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\index.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\jwt\\has-scope.policy.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\jwt\\has-scope.policy.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\policies.decorator.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\policies.guard.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\policies.guard.ts", [], ["1017"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\policies\\types.ts", [], ["1018"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\webhook\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\webhook\\webhook.guard.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\guards\\webhook\\webhook.guard.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\age-gate\\age-gate.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\age-gate\\age-gate.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\age-gate\\age-gate.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\analytic\\analytic.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\analytic\\analytic.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\analytic\\analytic.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\analytic\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\branding\\branding.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\branding\\branding.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\branding\\branding.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\callback\\callback.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\callback\\callback.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\callback\\callback.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\config\\config.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\config\\config.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\config\\config.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\config\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\email\\email-enqueue.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\email\\email-enqueue.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\email\\enqueue-email.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\email\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\family-group\\family-group.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\family-group\\family-group.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\family-group\\family-group.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\kafka\\kafka-producer.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\keycloak\\client-keycloak.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\keycloak\\keycloak.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\keycloak\\keycloak.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\kws-signature\\kws-signature.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\kws-signature\\kws-signature.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\kws-signature\\kws-signature.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\kws-signature\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\pre-verification\\pre-verification.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\pre-verification\\pre-verification.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\pre-verification\\pre-verification.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\pre-verification\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\settings\\settings.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\settings\\settings.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\settings\\settings.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\settings\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\translations\\translations.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\translations\\translations.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\services\\translations\\translations.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\types\\index.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\common\\utils\\testing\\index.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\countries\\countries.controller.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\countries\\countries.controller.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\countries\\countries.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\countries\\countries.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\countries\\countries.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\events\\events.controller.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\events\\events.controller.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\events\\events.dto.ts", [], ["1019"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\events\\events.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.controller.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.controller.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.dto.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\healthcheck.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\healthcheck\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\internal-admin\\internal-admin.controller.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\internal-admin\\internal-admin.controller.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\internal-admin\\internal-admin.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\internal-admin\\internal-admin.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\internal-admin\\internal-admin.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\base-job.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\base-job.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\delete-old-token-refresh-entries\\delete-old-token-refresh-entries.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\delete-old-token-refresh-entries\\delete-old-token-refresh-entries.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\generic-job-runner.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\generic-job-runner.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\job-list.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\jobs\\run-job.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\main.ts", [], ["1020"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.controller.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.controller.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.entity.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.repository.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.repository.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\jwk.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\legacy-jwk.entity.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth-token-handlers.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth-token-handlers.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth.controller.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth.controller.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\oauth.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\refresh-token.entity.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\type-check.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\oauth\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\org-env\\org-env.entity.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\org-env\\org-env.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\org-env\\org-env.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\org-env\\org-env.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\org-env\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\root-app.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\activation.entity.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\badger.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\badger.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\badger.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\talon.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\talon.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\talon.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.controller.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.controller.ts", [], ["1021", "1022"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.dto.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.entity.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.entity.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.service.spec.ts", [], ["1023", "1024", "1025"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\user\\user.service.ts", [], ["1026"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\utils.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\utils.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\types.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.controller.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.controller.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.entity.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.module.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.service.spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\src\\webhook\\webhook.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\app\\app.controller.e2e-spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\countries\\countries.controller.e2e-spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\events\\events.controller.e2e-spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\healthcheck\\healthcheck.controller.e2e-spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\internal-admin\\internal-admin.controller.e2e-spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\oauth\\jwk.controller.e2e-spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\oauth\\oauth.controller.e2e-spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\user\\user.controller.e2e-spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\user\\user.entity.e2e-spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\utils\\constants.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\utils\\index.ts", [], ["1027", "1028"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\utils\\kafka-helper.ts", [], ["1029"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\utils\\request-helper.ts", [], ["1030"], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\utils\\test-config.service.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test\\webhooks\\webhooks.controller.e2e-spec.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\fixtures\\app.fixture.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\fixtures\\oauth-callback-url.fixture.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\fixtures\\org-env.fixture.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\fixtures\\webhook.fixture.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\formatters\\junit-formatter.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\migration\\*************-add-uat-seeds.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\ormconfig.uat.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\app-users.steps.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\child-account-graduated.steps.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\countries.steps.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\families-group-deleted.steps.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\unresponsive-parent-deleted.steps.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\user-added-to-family.steps.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\user-permission-changed.steps.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\step-definitions\\user-removed-from-family.steps.ts", [], [], "C:\\Users\\<USER>\\git\\Codurance\\EpicGames\\freekws-classic-wrapper-backend\\test-acceptance\\utils\\index.ts", [], [], {"ruleId": "1031", "severity": 2, "message": "1032", "line": 11, "column": 18, "nodeType": "1033", "messageId": "1034", "endLine": 11, "endColumn": 21, "suggestions": "1035", "suppressions": "1036"}, {"ruleId": "1031", "severity": 2, "message": "1032", "line": 169, "column": 50, "nodeType": "1033", "messageId": "1034", "endLine": 169, "endColumn": 53, "suggestions": "1037", "suppressions": "1038"}, {"ruleId": "1031", "severity": 2, "message": "1032", "line": 182, "column": 41, "nodeType": "1033", "messageId": "1034", "endLine": 182, "endColumn": 44, "suggestions": "1039", "suppressions": "1040"}, {"ruleId": "1031", "severity": 2, "message": "1032", "line": 59, "column": 31, "nodeType": "1033", "messageId": "1034", "endLine": 59, "endColumn": 34, "suggestions": "1041", "suppressions": "1042"}, {"ruleId": "1043", "severity": 2, "message": "1044", "line": 14, "column": 13, "nodeType": "1045", "messageId": "1046", "endLine": 14, "endColumn": 21, "suppressions": "1047"}, {"ruleId": "1031", "severity": 2, "message": "1032", "line": 32, "column": 19, "nodeType": "1033", "messageId": "1034", "endLine": 32, "endColumn": 22, "suggestions": "1048", "suppressions": "1049"}, {"ruleId": "1050", "severity": 2, "message": "1051", "line": 13, "column": 1, "nodeType": "1052", "messageId": "1053", "endLine": 13, "endColumn": 7, "suggestions": "1054", "suppressions": "1055"}, {"ruleId": "1056", "severity": 1, "message": "1057", "line": 270, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 270, "endColumn": 15, "suggestions": "1060", "suppressions": "1061"}, {"ruleId": "1056", "severity": 1, "message": "1057", "line": 331, "column": 22, "nodeType": "1058", "messageId": "1059", "endLine": 331, "endColumn": 50, "suppressions": "1062"}, {"ruleId": "1063", "severity": 2, "message": "1064", "line": 433, "column": 5, "nodeType": "1065", "messageId": "1066", "endLine": 433, "endColumn": 35, "suppressions": "1067"}, {"ruleId": "1063", "severity": 2, "message": "1068", "line": 442, "column": 5, "nodeType": "1065", "messageId": "1066", "endLine": 442, "endColumn": 38, "suppressions": "1069"}, {"ruleId": "1063", "severity": 2, "message": "1070", "line": 450, "column": 5, "nodeType": "1065", "messageId": "1066", "endLine": 450, "endColumn": 36, "suppressions": "1071"}, {"ruleId": "1056", "severity": 1, "message": "1057", "line": 464, "column": 17, "nodeType": "1058", "messageId": "1059", "endLine": 464, "endColumn": 31, "suppressions": "1072"}, {"ruleId": "1056", "severity": 1, "message": "1057", "line": 758, "column": 15, "nodeType": "1058", "messageId": "1059", "endLine": 758, "endColumn": 25, "suppressions": "1073"}, {"ruleId": "1056", "severity": 1, "message": "1057", "line": 761, "column": 16, "nodeType": "1058", "messageId": "1059", "endLine": 761, "endColumn": 26, "suppressions": "1074"}, {"ruleId": "1031", "severity": 2, "message": "1032", "line": 8, "column": 36, "nodeType": "1033", "messageId": "1034", "endLine": 8, "endColumn": 39, "suggestions": "1075", "suppressions": "1076"}, {"ruleId": "1031", "severity": 2, "message": "1032", "line": 11, "column": 12, "nodeType": "1033", "messageId": "1034", "endLine": 11, "endColumn": 15, "suggestions": "1077", "suppressions": "1078"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1079", "1080"], ["1081"], ["1082", "1083"], ["1084"], ["1085", "1086"], ["1087"], ["1088", "1089"], ["1090"], "@typescript-eslint/ban-types", "Don't use `Function` as a type. The `Function` type accepts any function-like value.\nIt provides no type safety when calling the function, which can be a common source of bugs.\nIt also accepts things like class declarations, which will throw at runtime as they will not be called with `new`.\nIf you are expecting the function to accept certain arguments, you should explicitly define the function shape.", "Identifier", "bannedTypeMessage", ["1091"], ["1092", "1093"], ["1094"], "unicorn/prefer-top-level-await", "Prefer top-level await over an async function `main` call.", "CallExpression", "identifier", ["1095"], ["1096"], "@typescript-eslint/no-non-null-assertion", "Forbidden non-null assertion.", "TSNonNullExpression", "noNonNull", ["1097"], ["1098"], ["1099"], "unicorn/consistent-function-scoping", "Move function 'createKwsPasswordHash' to the outer scope.", "FunctionDeclaration", "consistent-function-scoping", ["1100"], "Move function 'createPopJamPasswordHash' to the outer scope.", ["1101"], "Move function 'createSha1PasswordHash' to the outer scope.", ["1102"], ["1103"], ["1104"], ["1105"], ["1106", "1107"], ["1108"], ["1109", "1110"], ["1111"], {"messageId": "1112", "fix": "1113", "desc": "1114"}, {"messageId": "1115", "fix": "1116", "desc": "1117"}, {"kind": "1118", "justification": "1119"}, {"messageId": "1112", "fix": "1120", "desc": "1114"}, {"messageId": "1115", "fix": "1121", "desc": "1117"}, {"kind": "1118", "justification": "1119"}, {"messageId": "1112", "fix": "1122", "desc": "1114"}, {"messageId": "1115", "fix": "1123", "desc": "1117"}, {"kind": "1118", "justification": "1119"}, {"messageId": "1112", "fix": "1124", "desc": "1114"}, {"messageId": "1115", "fix": "1125", "desc": "1117"}, {"kind": "1118", "justification": "1119"}, {"kind": "1118", "justification": "1119"}, {"messageId": "1112", "fix": "1126", "desc": "1114"}, {"messageId": "1115", "fix": "1127", "desc": "1117"}, {"kind": "1118", "justification": "1119"}, {"messageId": "1128", "fix": "1129", "data": "1130", "desc": "1131"}, {"kind": "1118", "justification": "1119"}, {"messageId": "1132", "fix": "1133", "desc": "1134"}, {"kind": "1118", "justification": "1119"}, {"kind": "1118", "justification": "1119"}, {"kind": "1118", "justification": "1119"}, {"kind": "1118", "justification": "1119"}, {"kind": "1118", "justification": "1119"}, {"kind": "1118", "justification": "1119"}, {"kind": "1118", "justification": "1119"}, {"kind": "1118", "justification": "1119"}, {"messageId": "1112", "fix": "1135", "desc": "1114"}, {"messageId": "1115", "fix": "1136", "desc": "1117"}, {"kind": "1118", "justification": "1119"}, {"messageId": "1112", "fix": "1137", "desc": "1114"}, {"messageId": "1115", "fix": "1138", "desc": "1117"}, {"kind": "1118", "justification": "1119"}, "suggestUnknown", {"range": "1139", "text": "1140"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1141", "text": "1142"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "directive", "", {"range": "1143", "text": "1140"}, {"range": "1144", "text": "1142"}, {"range": "1145", "text": "1140"}, {"range": "1146", "text": "1142"}, {"range": "1147", "text": "1140"}, {"range": "1148", "text": "1142"}, {"range": "1149", "text": "1140"}, {"range": "1150", "text": "1142"}, "add-await", {"range": "1151", "text": "1152"}, {"name": "1153"}, "Insert `await`.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", {"range": "1154", "text": "1155"}, "Consider using the optional chain operator `?.` instead. This operator includes runtime checks, so it is safer than the compile-only non-null assertion operator.", {"range": "1156", "text": "1140"}, {"range": "1157", "text": "1142"}, {"range": "1158", "text": "1140"}, {"range": "1159", "text": "1142"}, [505, 508], "unknown", [505, 508], "never", [5958, 5961], [5958, 5961], [6436, 6439], [6436, 6439], [2287, 2290], [2287, 2290], [816, 819], [816, 819], [464, 464], "await ", "main", [9577, 9578], "?", [311, 314], [311, 314], [354, 357], [354, 357]]