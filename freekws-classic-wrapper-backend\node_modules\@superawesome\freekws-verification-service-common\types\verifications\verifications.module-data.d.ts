import { IClientsPluginModule, IClientsPluginRequest, THttpMethod } from '@superawesome/freekws-clients-base';
import { FirstStepInputDTO, InitiateInputDTO, InitiateOutputDTO, OtherStepInputDTO, StepOutputDTO } from './verifications.dto';
declare class InitiateRequest implements IClientsPluginRequest<InitiateRequest> {
    readonly path = "/v1/verifications/initiate";
    readonly httpMethod: THttpMethod;
    returnType: InitiateOutputDTO;
    body: InitiateInputDTO;
}
declare class Step1Request implements IClientsPluginRequest<Step1Request> {
    readonly path = "/v1/verifications/steps/1";
    readonly httpMethod: THttpMethod;
    returnType: StepOutputDTO;
    body: FirstStepInputDTO;
}
declare class StepNoRequest implements IClientsPluginRequest<StepNoRequest> {
    readonly path = "/v1/verifications/steps/:stepNo";
    readonly httpMethod: THttpMethod;
    returnType: StepOutputDTO;
    body: OtherStepInputDTO;
    params: {
        stepNo: `${number}`;
    };
}
export declare class VerificationsModuleData implements IClientsPluginModule {
    readonly requests: {
        initiate: InitiateRequest;
        step1: Step1Request;
        stepNo: StepNoRequest;
    };
}
export {};
