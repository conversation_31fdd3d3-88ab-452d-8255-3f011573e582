"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FamilyGroupService = void 0;
const common_1 = require("@nestjs/common");
const freekws_auth_library_1 = require("@superawesome/freekws-auth-library");
const freekws_clients_nestjs_1 = require("@superawesome/freekws-clients-nestjs");
const freekws_family_service_common_1 = require("@superawesome/freekws-family-service-common");
const client_keycloak_service_1 = require("../keycloak/client-keycloak.service");
const keycloak_module_1 = require("../keycloak/keycloak.module");
let FamilyGroupService = class FamilyGroupService {
    clientKeycloakService;
    client;
    keycloakClient;
    constructor(clientKeycloakService, client, keycloakClient) {
        this.clientKeycloakService = clientKeycloakService;
        this.client = client;
        this.keycloakClient = keycloakClient;
    }
    async getGuardianLinks(orgEnvId, userId) {
        const axoisResponse = await this.client.getModule('internalAdminFamilyGroup').findGuardianLinks({
            query: {
                userId: `${userId}`
            },
        }, {
            headers: {
                Authorization: `Bearer ${await this.keycloakClient.getUpToDateServiceAccessToken()}`,
            },
        });
        return axoisResponse.data.response;
    }
    async getGuardianLinksFromEmail(orgEnvId, email) {
        const axoisResponse = await this.client.getModule('internalAdminFamilyGroup').findGuardianLinks({
            query: { orgEnvId: `${orgEnvId}`, email: email },
        }, {
            headers: {
                Authorization: `Bearer ${await this.keycloakClient.getUpToDateServiceAccessToken()}`,
            },
        });
        return axoisResponse.data.response;
    }
};
exports.FamilyGroupService = FamilyGroupService;
exports.FamilyGroupService = FamilyGroupService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)(freekws_family_service_common_1.FAMILY_SERVICE_CLIENT_INJECT_KEY)),
    __param(2, (0, common_1.Inject)(keycloak_module_1.KEYCLOAK_PROVIDER)),
    __metadata("design:paramtypes", [client_keycloak_service_1.ClientKeycloakService,
        freekws_clients_nestjs_1.NestJsClient,
        freekws_auth_library_1.KeycloakService])
], FamilyGroupService);
//# sourceMappingURL=family-group.service.js.map