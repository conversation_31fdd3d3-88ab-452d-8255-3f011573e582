<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="92" failures="0" errors="0" time="74.592">
  <testsuite name="AppController (e2e)" errors="0" failures="0" skipped="0" timestamp="2025-08-15T13:46:50" time="20.287" tests="41">
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users should return expected response" name="AppController (e2e) POST /v2/apps/:appId/users should return expected response" time="0.422" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users should respond with error for unauthenticated response" name="AppController (e2e) POST /v2/apps/:appId/users should respond with error for unauthenticated response" time="0.021" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users should respond with error for invalid token" name="AppController (e2e) POST /v2/apps/:appId/users should respond with error for invalid token" time="0.021" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users should respond with error for minor user without parent email" name="AppController (e2e) POST /v2/apps/:appId/users should respond with error for minor user without parent email" time="0.022" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users should allow undefined dateOfBirth for allowed customer (beme-health)" name="AppController (e2e) POST /v2/apps/:appId/users should allow undefined dateOfBirth for allowed customer (beme-health)" time="0.061" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should return expected response" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should return expected response" time="0.091" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should respond with error for not exist permission" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should respond with error for not exist permission" time="0.072" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should respond with error for invalid token scope" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should respond with error for invalid token scope" time="0.014" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should respond with error for invalid app ID" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should respond with error for invalid app ID" time="0.02" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should respond with error for missing parent email" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should respond with error for missing parent email" time="0.063" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should respond with success for graduated user" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should respond with success for graduated user" time="0.037" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should automatically add T&amp;C permission when termsAndConditionsRequired is true and user has not accepted T&amp;C" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/request-permissions should automatically add T&amp;C permission when termsAndConditionsRequired is true and user has not accepted T&amp;C" time="0.066" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/update-parent-email (e2e) should return expected response code" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/update-parent-email (e2e) should return expected response code" time="0.031" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/update-parent-email (e2e) should respond with error for invalid token scope" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/update-parent-email (e2e) should respond with error for invalid token scope" time="0.016" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/update-parent-email (e2e) should respond with error for invalid app ID" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/update-parent-email (e2e) should respond with error for invalid app ID" time="0.016" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/users/:userId should return expected response for app scope and unverified parent" name="AppController (e2e) GET /v2/apps/:appId/users/:userId should return expected response for app scope and unverified parent" time="0.032" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/users/:userId should return expected response for control panel scope and verified parent" name="AppController (e2e) GET /v2/apps/:appId/users/:userId should return expected response for control panel scope and verified parent" time="0.036" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/users/:userId should return default language for user with unsupported language" name="AppController (e2e) GET /v2/apps/:appId/users/:userId should return default language for user with unsupported language" time="0.036" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/users/:userId should respond with error for invalid token scope" name="AppController (e2e) GET /v2/apps/:appId/users/:userId should respond with error for invalid token scope" time="0.014" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) DELETE /v2/apps/:appId/users/:userId deletes user&apos;s settings" name="AppController (e2e) DELETE /v2/apps/:appId/users/:userId deletes user&apos;s settings" time="0.067" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) DELETE /v2/apps/:appId/users/:userId 404&apos;s when attempting to delete a user without the specified activation" name="AppController (e2e) DELETE /v2/apps/:appId/users/:userId 404&apos;s when attempting to delete a user without the specified activation" time="0.017" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) DELETE /v2/apps/:appId/users/:userId deletes user when it is the users final activation" name="AppController (e2e) DELETE /v2/apps/:appId/users/:userId deletes user when it is the users final activation" time="0.056" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/users/:userId/permissions returns expected permissions for specified user" name="AppController (e2e) GET /v2/apps/:appId/users/:userId/permissions returns expected permissions for specified user" time="0.04" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/users/:userId/permissions returns expected permissions for specified user with extended details" name="AppController (e2e) GET /v2/apps/:appId/users/:userId/permissions returns expected permissions for specified user with extended details" time="0.04" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/review-permissions return expected response" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/review-permissions return expected response" time="0.024" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should activate a user to another app successfully" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should activate a user to another app successfully" time="0.105" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should return 409 Conflict if user is already activated for the app" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should return 409 Conflict if user is already activated for the app" time="0.034" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should activate user with requested permissions" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should activate user with requested permissions" time="0.111" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should respond with error for invalid token" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should respond with error for invalid token" time="0.033" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should respond with error for invalid app ID" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should respond with error for invalid app ID" time="0.03" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should respond with error for non-existent user ID" name="AppController (e2e) POST /v2/apps/:appId/users/:userId/activate should respond with error for non-existent user ID" time="0.036" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/permissions/translated should return translated permissions for all permissions when no specific permissions requested" name="AppController (e2e) GET /v2/apps/:appId/permissions/translated should return translated permissions for all permissions when no specific permissions requested" time="0.023" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/permissions/translated should filter permissions when specific permissions are requested" name="AppController (e2e) GET /v2/apps/:appId/permissions/translated should filter permissions when specific permissions are requested" time="0.023" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/permissions/translated should use Spanish translations when accept-language header is es" name="AppController (e2e) GET /v2/apps/:appId/permissions/translated should use Spanish translations when accept-language header is es" time="0.02" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/permissions/translated should handle CloudFront-Viewer-Country header" name="AppController (e2e) GET /v2/apps/:appId/permissions/translated should handle CloudFront-Viewer-Country header" time="0.02" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v2/apps/:appId/permissions/translated should return empty array when requested permissions do not exist" name="AppController (e2e) GET /v2/apps/:appId/permissions/translated should return empty array when requested permissions do not exist" time="0.02" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v1/apps/config and GET /v2/apps/config should return app config with translations for v2 endpoint" name="AppController (e2e) GET /v1/apps/config and GET /v2/apps/config should return app config with translations for v2 endpoint" time="0.03" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v1/apps/config and GET /v2/apps/config should return app config with translations for v1 endpoint" name="AppController (e2e) GET /v1/apps/config and GET /v2/apps/config should return app config with translations for v1 endpoint" time="0.028" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v1/apps/config and GET /v2/apps/config should return app config with no translations when none exist" name="AppController (e2e) GET /v1/apps/config and GET /v2/apps/config should return app config with no translations when none exist" time="0.032" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v1/apps/config and GET /v2/apps/config should return 404 for non-existent oauth client id" name="AppController (e2e) GET /v1/apps/config and GET /v2/apps/config should return 404 for non-existent oauth client id" time="0.021" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="AppController (e2e) GET /v1/apps/config and GET /v2/apps/config should return 400 for missing oauth client id" name="AppController (e2e) GET /v1/apps/config and GET /v2/apps/config should return 400 for missing oauth client id" time="0.024" file="test/app/app.controller.e2e-spec.ts">
    </testcase>
  </testsuite>
  <testsuite name="UsersController (e2e)" errors="0" failures="0" skipped="0" timestamp="2025-08-15T13:47:10" time="18.374" tests="13">
    <testcase classname="UsersController (e2e) GET /v1/users/:userId/apps grants user activation" name="UsersController (e2e) GET /v1/users/:userId/apps grants user activation" time="0.181" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) GET /v1/users/:userId/apps rejects user activation for other app with token not from same app" name="UsersController (e2e) GET /v1/users/:userId/apps rejects user activation for other app with token not from same app" time="0.017" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) GET /v1/users/check-username should return available true when username is available and passes moderation" name="UsersController (e2e) GET /v1/users/check-username should return available true when username is available and passes moderation" time="0.027" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) GET /v1/users/check-username should return available false when username is already taken" name="UsersController (e2e) GET /v1/users/check-username should return available false when username is already taken" time="0.028" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) GET /v1/users/check-username should return available false when username fails moderation" name="UsersController (e2e) GET /v1/users/check-username should return available false when username fails moderation" time="0.02" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) GET /v1/users/check-username should handle language parameter correctly" name="UsersController (e2e) GET /v1/users/check-username should handle language parameter correctly" time="0.028" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) POST /v2/users should register a new user successfully" name="UsersController (e2e) POST /v2/users should register a new user successfully" time="0.06" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) POST /v2/users should reject registration when username and password are both missing" name="UsersController (e2e) POST /v2/users should reject registration when username and password are both missing" time="0.012" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) POST /v2/users should reject registration when username is already taken" name="UsersController (e2e) POST /v2/users should reject registration when username is already taken" time="0.037" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) POST /v1/users/forgot-password should return a 204 No-Content" name="UsersController (e2e) POST /v1/users/forgot-password should return a 204 No-Content" time="5.042" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) POST /v1/users/reset-password should return a 204 No-Content" name="UsersController (e2e) POST /v1/users/reset-password should return a 204 No-Content" time="5.086" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) POST /v1/users/reset-password should return a 400 if app not found" name="UsersController (e2e) POST /v1/users/reset-password should return a 400 if app not found" time="0.046" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="UsersController (e2e) POST /v1/users/reset-password should return a 400 if user not found" name="UsersController (e2e) POST /v1/users/reset-password should return a 400 if user not found" time="0.02" file="test/user/user.controller.e2e-spec.ts">
    </testcase>
  </testsuite>
  <testsuite name="WebhooksController (e2e)" errors="0" failures="0" skipped="0" timestamp="2025-08-15T13:47:28" time="11.191" tests="6">
    <testcase classname="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/child-account-graduate should return the expected response" name="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/child-account-graduate should return the expected response" time="1.217" file="test/webhooks/webhooks.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/user-removed-from-family should return the expected response" name="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/user-removed-from-family should return the expected response" time="0.829" file="test/webhooks/webhooks.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/settings-effective-values-changed should return the expected response" name="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/settings-effective-values-changed should return the expected response" time="0.884" file="test/webhooks/webhooks.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/user-added-to-family should return the expected response" name="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/user-added-to-family should return the expected response" time="0.835" file="test/webhooks/webhooks.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/guardian-request-expired should return the expected response" name="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/guardian-request-expired should return the expected response" time="0.802" file="test/webhooks/webhooks.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/families-group-deleted should return the expected response" name="WebhooksController (e2e) POST /v1/webhooks/:orgEnvId/families-group-deleted should return the expected response" time="0.881" file="test/webhooks/webhooks.controller.e2e-spec.ts">
    </testcase>
  </testsuite>
  <testsuite name="OauthController (e2e)" errors="0" failures="0" skipped="0" timestamp="2025-08-15T13:47:40" time="3.775" tests="11">
    <testcase classname="OauthController (e2e) POST /oauth/token returns app token for valid credentials" name="OauthController (e2e) POST /oauth/token returns app token for valid credentials" time="0.056" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="OauthController (e2e) POST /oauth/token returns mobile app token for valid credentials" name="OauthController (e2e) POST /oauth/token returns mobile app token for valid credentials" time="0.05" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="OauthController (e2e) POST /oauth/token returns user scoped token for password grant type" name="OauthController (e2e) POST /oauth/token returns user scoped token for password grant type" time="0.138" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="OauthController (e2e) POST /oauth/token returns new tokens for refresh token grant type with user scope" name="OauthController (e2e) POST /oauth/token returns new tokens for refresh token grant type with user scope" time="0.146" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="OauthController (e2e) POST /oauth/token returns new tokens for refresh token grant type with app scope" name="OauthController (e2e) POST /oauth/token returns new tokens for refresh token grant type with app scope" time="0.071" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="OauthController (e2e) POST /oauth/authorise should return an authorization code when provided valid credentials" name="OauthController (e2e) POST /oauth/authorise should return an authorization code when provided valid credentials" time="0.106" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="OauthController (e2e) POST /oauth/authorise should support PKCE with S256 method" name="OauthController (e2e) POST /oauth/authorise should support PKCE with S256 method" time="0.106" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="OauthController (e2e) POST /oauth/authorise should support PKCE with plain method" name="OauthController (e2e) POST /oauth/authorise should support PKCE with plain method" time="0.099" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="OauthController (e2e) POST /oauth/authorise should use query state parameter when no state is provided in body" name="OauthController (e2e) POST /oauth/authorise should use query state parameter when no state is provided in body" time="0.107" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="OauthController (e2e) POST /oauth/authorise should support the complete authorization flow with code exchange" name="OauthController (e2e) POST /oauth/authorise should support the complete authorization flow with code exchange" time="0.167" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="OauthController (e2e) POST /oauth/authorise should support the complete authorization flow with PKCE" name="OauthController (e2e) POST /oauth/authorise should support the complete authorization flow with PKCE" time="0.163" file="test/oauth/oauth.controller.e2e-spec.ts">
    </testcase>
  </testsuite>
  <testsuite name="InternalAdminController (e2e)" errors="0" failures="0" skipped="0" timestamp="2025-08-15T13:47:43" time="2.889" tests="8">
    <testcase classname="InternalAdminController (e2e) PUT /internal-admin/users/:userId/dob should get 401 for invalid user" name="InternalAdminController (e2e) PUT /internal-admin/users/:userId/dob should get 401 for invalid user" time="0.044" file="test/internal-admin/internal-admin.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="InternalAdminController (e2e) PUT /internal-admin/users/:userId/dob should get 200" name="InternalAdminController (e2e) PUT /internal-admin/users/:userId/dob should get 200" time="0.162" file="test/internal-admin/internal-admin.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="InternalAdminController (e2e) POST /internal-admin/org-envs/:orgEnvId/users/:userId/delete-account deletes a user account" name="InternalAdminController (e2e) POST /internal-admin/org-envs/:orgEnvId/users/:userId/delete-account deletes a user account" time="0.081" file="test/internal-admin/internal-admin.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="InternalAdminController (e2e) POST /internal-admin/org-envs/:orgEnvId/users/:userId/delete-account responds with bad request when password is provided" name="InternalAdminController (e2e) POST /internal-admin/org-envs/:orgEnvId/users/:userId/delete-account responds with bad request when password is provided" time="0.058" file="test/internal-admin/internal-admin.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="InternalAdminController (e2e) GET /internal-admin/users should return 400 if username is less than 3 characters" name="InternalAdminController (e2e) GET /internal-admin/users should return 400 if username is less than 3 characters" time="0.051" file="test/internal-admin/internal-admin.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="InternalAdminController (e2e) GET /internal-admin/users should return 401 if not authenticated with keycloak token" name="InternalAdminController (e2e) GET /internal-admin/users should return 401 if not authenticated with keycloak token" time="0.042" file="test/internal-admin/internal-admin.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="InternalAdminController (e2e) GET /internal-admin/users should return 403 if not authenticated with devportal-api scope" name="InternalAdminController (e2e) GET /internal-admin/users should return 403 if not authenticated with devportal-api scope" time="0.053" file="test/internal-admin/internal-admin.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="InternalAdminController (e2e) GET /internal-admin/users should return matching users with parent emails" name="InternalAdminController (e2e) GET /internal-admin/users should return matching users with parent emails" time="0.099" file="test/internal-admin/internal-admin.controller.e2e-spec.ts">
    </testcase>
  </testsuite>
  <testsuite name="JwkController (e2e)" errors="0" failures="0" skipped="0" timestamp="2025-08-15T13:47:46" time="2.85" tests="2">
    <testcase classname="JwkController (e2e) GET /v1/jwks should return a valid JWK set" name="JwkController (e2e) GET /v1/jwks should return a valid JWK set" time="0.328" file="test/oauth/jwk.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="JwkController (e2e) GET /v1/jwks should return an empty keys array when no JWKs exist" name="JwkController (e2e) GET /v1/jwks should return an empty keys array when no JWKs exist" time="0.013" file="test/oauth/jwk.controller.e2e-spec.ts">
    </testcase>
  </testsuite>
  <testsuite name="EventsController (e2e)" errors="0" failures="0" skipped="0" timestamp="2025-08-15T13:47:49" time="6.544" tests="6">
    <testcase classname="EventsController (e2e) POST /v2/events should process events successfully with valid auth string" name="EventsController (e2e) POST /v2/events should process events successfully with valid auth string" time="1.001" file="test/events/events.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="EventsController (e2e) POST /v2/events should return 401 with invalid auth string" name="EventsController (e2e) POST /v2/events should return 401 with invalid auth string" time="0.837" file="test/events/events.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="EventsController (e2e) POST /v2/events should return 401 when auth header is missing" name="EventsController (e2e) POST /v2/events should return 401 when auth header is missing" time="0.812" file="test/events/events.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="EventsController (e2e) POST /v2/events should process events with minimal data" name="EventsController (e2e) POST /v2/events should process events with minimal data" time="0.794" file="test/events/events.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="EventsController (e2e) POST /v2/events should return 400 for invalid request body" name="EventsController (e2e) POST /v2/events should return 400 for invalid request body" time="0.747" file="test/events/events.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="EventsController (e2e) POST /v2/events should return 400 for invalid event structure" name="EventsController (e2e) POST /v2/events should return 400 for invalid event structure" time="0.815" file="test/events/events.controller.e2e-spec.ts">
    </testcase>
  </testsuite>
  <testsuite name="CountriesController (e2e)" errors="0" failures="0" skipped="0" timestamp="2025-08-15T13:47:56" time="4.14" tests="3">
    <testcase classname="CountriesController (e2e) GET /v1/countries/child-age should return the expected response" name="CountriesController (e2e) GET /v1/countries/child-age should return the expected response" time="0.867" file="test/countries/countries.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="CountriesController (e2e) GET /v1/countries/child-age should return the expected response when date is MM-DD-YYYY" name="CountriesController (e2e) GET /v1/countries/child-age should return the expected response when date is MM-DD-YYYY" time="0.825" file="test/countries/countries.controller.e2e-spec.ts">
    </testcase>
    <testcase classname="CountriesController (e2e) GET /v1/countries/child-age should return the 400 response when date is invalid" name="CountriesController (e2e) GET /v1/countries/child-age should return the 400 response when date is invalid" time="0.804" file="test/countries/countries.controller.e2e-spec.ts">
    </testcase>
  </testsuite>
  <testsuite name="User entity (e2e)" errors="0" failures="0" skipped="0" timestamp="2025-08-15T13:48:00" time="2.225" tests="1">
    <testcase classname="User entity (e2e) Insert new user should assign a pseudorandom user id" name="User entity (e2e) Insert new user should assign a pseudorandom user id" time="0.595" file="test/user/user.entity.e2e-spec.ts">
    </testcase>
  </testsuite>
  <testsuite name="HealthcheckController (e2e)" errors="0" failures="0" skipped="0" timestamp="2025-08-15T13:48:02" time="1.783" tests="1">
    <testcase classname="HealthcheckController (e2e) GET /healthcheck should return the expected response" name="HealthcheckController (e2e) GET /healthcheck should return the expected response" time="0.019" file="test/healthcheck/healthcheck.controller.e2e-spec.ts">
    </testcase>
  </testsuite>
</testsuites>