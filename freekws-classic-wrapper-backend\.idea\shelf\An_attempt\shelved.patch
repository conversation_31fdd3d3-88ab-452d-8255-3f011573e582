Index: src/common/services/settings/settings.service.test-utils.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/common/services/settings/settings.service.test-utils.ts b/src/common/services/settings/settings.service.test-utils.ts
new file mode 100644
--- /dev/null	(date 1755268615435)
+++ b/src/common/services/settings/settings.service.test-utils.ts	(date 1755268615435)
@@ -0,0 +1,114 @@
+import {
+    EResponseFormat,
+    ESettingBooleanOrder,
+    ESettingConsentType,
+    ESettingValueType,
+    SettingIdentifierDTO,
+    UserSettingValueDTO,
+    UserSettingValueShortDTO,
+} from '@superawesome/freekws-settings-common';
+
+// Small helpers to reduce noise in tests.
+
+export const ok = <T>(response: T): any => ({
+    data: {
+        response,
+        meta: { timestamp: '', requestId: '' },
+    },
+    status: 200,
+});
+
+export const makeSettingIdentifier = (overrides: Partial<SettingIdentifierDTO> & { namespace?: string; settingName?: string }) => ({
+    namespace: 'default',
+    settingName: 'setting',
+    ...overrides,
+});
+
+export const makeDefinition = (overrides: Partial<UserSettingValueDTO['definition']>) => ({
+    ageBracket: {
+        consentType: ESettingConsentType.OPT_OUT,
+        defaultPreference: '',
+        ...(overrides?.ageBracket || {}),
+    },
+    orgId: '',
+    namespace: '',
+    settingName: '',
+    valueType: ESettingValueType.BOOLEAN,
+    translations: overrides?.translations || {},
+    restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
+    userHidden: false,
+    userReadOnly: false,
+    required: false,
+    ...overrides,
+});
+
+export const makeUserSettingValue = (
+    overrides: Partial<UserSettingValueDTO> & { namespace?: string; settingName?: string },
+) =>
+({
+    namespace: 'default',
+    settingName: 'setting',
+    preferredValue: true,
+    preferredValueFromOrgLevel: false,
+    effectiveValue: false,
+    isOrgLevel: false,
+    definition: makeDefinition({}),
+    ...overrides,
+} as unknown as UserSettingValueDTO);
+
+export const makeShortUserSettingValue = (
+    overrides: Partial<UserSettingValueShortDTO> & { namespace?: string; settingName?: string },
+) =>
+({
+    namespace: 'default',
+    settingName: 'setting',
+    preferredValue: false,
+    preferredValueFromOrgLevel: false,
+    effectiveValue: false,
+    effectiveSource: undefined,
+    isOrgLevel: false,
+    ...overrides,
+} as unknown as UserSettingValueShortDTO);
+
+export const productDefinitionWithChat = (irrevocableText = true) => [
+    {
+        version: 1,
+        orgId: 'test-org-id',
+        productId: 'test-product-id',
+        namespace: 'chat',
+        settings: [
+            {
+                settingName: 'voice',
+                valueType: 'boolean',
+                userHidden: false,
+                required: false,
+                autoReviewConsent: false,
+                label: { en: 'Voice Chat' },
+                parentNotice: { en: 'Allow voice chat' },
+                userNotice: { en: 'Voice chat permission' },
+                regions: [],
+                irrevocable: false,
+            },
+            {
+                settingName: 'text',
+                valueType: 'boolean',
+                userHidden: false,
+                required: false,
+                autoReviewConsent: false,
+                label: { en: 'Text Chat' },
+                parentNotice: { en: 'Allow text chat' },
+                userNotice: { en: 'Text chat permission' },
+                regions: [],
+                irrevocable: irrevocableText,
+            },
+        ],
+    },
+];
+
+export const optInDefinition = {
+    ageBracket: {
+        consentType: ESettingConsentType.OPT_IN_UNVERIFIED,
+    },
+};
+
+
Index: src/common/services/settings/settings.service.spec.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { BadRequestException, NotFoundException } from '@nestjs/common';\r\nimport { TestingModule } from '@nestjs/testing';\r\nimport { getRepositoryToken } from '@nestjs/typeorm';\r\nimport { AuthLibraryUtils, EKeycloakScope } from '@superawesome/freekws-auth-library';\r\nimport { createClientMock } from '@superawesome/freekws-clients-base/src/test-utils/mock';\r\nimport { EmailMessageService } from '@superawesome/freekws-queue-messages/email';\r\nimport { Tiso31662 } from '@superawesome/freekws-regional-config';\r\nimport {\r\n  EResponseFormat,\r\n  ESettingBooleanOrder,\r\n  ESettingConsentType,\r\n  ESettingValueType,\r\n  EUserSettingValueEffectiveSource,\r\n  SettingIdentifierDTO,\r\n  SETTINGS_BACKEND_API_CLIENT_INJECT_KEY,\r\n  settingsBackendPlugin,\r\n  UserSettingValueDTO,\r\n  UserSettingValueShortDTO,\r\n} from '@superawesome/freekws-settings-common';\r\nimport { AxiosError, AxiosResponse } from 'axios';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { SettingsService } from './settings.service';\r\nimport {\r\n  ESettingsServiceErrorCodes,\r\n  SettingsErrorResponse,\r\n  SettingsServiceConsentNotRequestedError,\r\n  SettingsServiceParentNotInFamilyError,\r\n} from './types';\r\nimport { App } from '../../../app/app.entity';\r\nimport { OrgEnv } from '../../../org-env/org-env.entity';\r\nimport { Testing } from '../../utils';\r\nimport { ClientKeycloakService } from '../keycloak/client-keycloak.service';\r\n\r\nconst mockSettingsApi = createClientMock(settingsBackendPlugin, jest.fn);\r\n\r\nconst optInDefinition = {\r\n  ageBracket: {\r\n    consentType: ESettingConsentType.OPT_IN_UNVERIFIED,\r\n  },\r\n};\r\ndescribe('SettingsService', () => {\r\n  const TOKEN = 'TOKEN';\r\n  let settingsService: SettingsService;\r\n  let keycloakService: ClientKeycloakService;\r\n  let appRepo: Repository<App>;\r\n  let emailMessageService: EmailMessageService;\r\n\r\n  const mockOrgEnv = {\r\n    id: 'test-org-env-id',\r\n    orgId: 'test-org-id',\r\n  } as OrgEnv;\r\n\r\n  const mockApp = {\r\n    id: 1,\r\n    productId: 'test-product-id',\r\n    productEnvId: 'test-product-env-id',\r\n    orgEnvId: 'test-org-env-id',\r\n    orgEnv: mockOrgEnv,\r\n  } as App;\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Testing.createModule({\r\n      providers: [SettingsService, { provide: SETTINGS_BACKEND_API_CLIENT_INJECT_KEY, useValue: mockSettingsApi }],\r\n    });\r\n\r\n    settingsService = module.get<SettingsService>(SettingsService);\r\n    keycloakService = module.get<ClientKeycloakService>(ClientKeycloakService);\r\n    appRepo = module.get<Repository<App>>(getRepositoryToken(App));\r\n    emailMessageService = module.get<EmailMessageService>(EmailMessageService);\r\n\r\n    keycloakService.getClientToken = jest.fn().mockResolvedValue(TOKEN);\r\n\r\n    jest.spyOn(appRepo, 'findOneOrFail').mockResolvedValue(mockApp);\r\n\r\n    jest.spyOn(settingsService, 'getProductSettingsDefinition').mockResolvedValue([\r\n      {\r\n        version: 1,\r\n        orgId: 'test-org-id',\r\n        productId: 'test-product-id',\r\n        namespace: 'chat',\r\n        settings: [\r\n          {\r\n            settingName: 'voice',\r\n            valueType: 'boolean',\r\n            userHidden: false,\r\n            required: false,\r\n            autoReviewConsent: false,\r\n            label: { en: 'Voice Chat' },\r\n            parentNotice: { en: 'Allow voice chat' },\r\n            userNotice: { en: 'Voice chat permission' },\r\n            regions: [],\r\n            irrevocable: false,\r\n          },\r\n          {\r\n            settingName: 'text',\r\n            valueType: 'boolean',\r\n            userHidden: false,\r\n            required: false,\r\n            autoReviewConsent: false,\r\n            label: { en: 'Text Chat' },\r\n            parentNotice: { en: 'Allow text chat' },\r\n            userNotice: { en: 'Text chat permission' },\r\n            regions: [],\r\n            irrevocable: true,\r\n          },\r\n        ],\r\n      },\r\n    ]);\r\n\r\n    // Mock getUserSettings to return empty array by default\r\n    jest.spyOn(settingsService, 'getUserSettings').mockResolvedValue([]);\r\n\r\n    jest.spyOn(emailMessageService, 'validateEmailDomain').mockResolvedValue(true);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('sendConsentEmail', () => {\r\n    it('should return effective values of consent request', async () => {\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [\r\n              {\r\n                namespace: 'chat',\r\n                settingName: 'voice',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: true,\r\n                effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,\r\n                isOrgLevel: false,\r\n              } as UserSettingValueShortDTO,\r\n            ],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      const result = await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          permissions: ['chat.voice', 'geolocation'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(result).toEqual([\r\n        {\r\n          namespace: 'chat',\r\n          settingName: 'voice',\r\n          preferredValue: true,\r\n          preferredValueFromOrgLevel: false,\r\n          effectiveValue: true,\r\n          effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,\r\n          isOrgLevel: false,\r\n        },\r\n      ]);\r\n\r\n      expect(appRepo.findOneOrFail).toHaveBeenCalledWith({\r\n        where: {\r\n          productId: '1',\r\n          orgEnvId: '',\r\n        },\r\n        relations: {\r\n          orgEnv: true,\r\n        },\r\n      });\r\n\r\n      expect(settingsService.getProductSettingsDefinition).toHaveBeenCalledWith({\r\n        orgId: 'test-org-id',\r\n        orgEnvId: 'test-org-env-id',\r\n        productId: 'test-product-id',\r\n        productEnvId: 'test-product-env-id',\r\n      });\r\n\r\n      expect(settingsService.getUserSettings).toHaveBeenCalledWith(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          permissions: ['chat.voice', 'geolocation'],\r\n          dateOfBirth: '2010-10-10',\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n    });\r\n\r\n    it('should convert unsupported country code to ZZ in consent request', async () => {\r\n      const mockSendConsentEmailAtProductLevel =\r\n        mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel;\r\n      mockSendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'unsupported-country-code' as Tiso31662,\r\n          permissions: ['chat.voice', 'geolocation'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        {\r\n          params: {\r\n            userId: '1',\r\n            productId: '1',\r\n          },\r\n          body: {\r\n            parentEmail: '<EMAIL>',\r\n            language: 'ca',\r\n            location: 'ZZ',\r\n            dob: '2010-10-10',\r\n            settings: [\r\n              { namespace: 'chat', settingName: 'voice' },\r\n              { namespace: 'default', settingName: 'geolocation' },\r\n              { namespace: 'chat', settingName: 'text' },\r\n            ],\r\n          },\r\n        },\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${TOKEN}`,\r\n          },\r\n        },\r\n      );\r\n    });\r\n\r\n    it('should throw an error if parent email not presented for minor user', async () => {\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockRejectedValueOnce({\r\n        response: {\r\n          data: {\r\n            freekwsErrorCode: 'consent_request_missing_parent_email',\r\n          },\r\n        },\r\n        status: 400,\r\n      });\r\n\r\n      await expect(\r\n        settingsService.sendConsentEmail(\r\n          {\r\n            userId: 1,\r\n            productId: '1',\r\n            dob: '2010-10-10',\r\n            language: 'ca',\r\n            location: 'US',\r\n            permissions: ['chat.voice', 'geolocation'],\r\n            parentEmail: '<EMAIL>',\r\n          },\r\n          {\r\n            clientId: '',\r\n            secret: '',\r\n          },\r\n        ),\r\n      ).rejects.toThrow(ESettingsServiceErrorCodes.CONSENT_REQUEST_MISSING_PARENT_EMAIL);\r\n    });\r\n\r\n    it('should throw an error if request failed', async () => {\r\n      const error = new AxiosError('SERVISE_UNAVAILABLE', '503');\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockRejectedValueOnce(error);\r\n\r\n      await expect(\r\n        settingsService.sendConsentEmail(\r\n          {\r\n            userId: 1,\r\n            productId: '1',\r\n            dob: '2010-10-10',\r\n            language: 'ca',\r\n            location: 'US',\r\n            permissions: ['chat.voice', 'geolocation'],\r\n            parentEmail: '<EMAIL>',\r\n          },\r\n          {\r\n            clientId: '',\r\n            secret: '',\r\n          },\r\n        ),\r\n      ).rejects.toThrow('SERVISE_UNAVAILABLE');\r\n    });\r\n\r\n    it('should include irrevocable settings that are not already granted', async () => {\r\n      // Mock getUserSettings to return no existing settings\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            settings: expect.arrayContaining([\r\n              { namespace: 'chat', settingName: 'voice' },\r\n              { namespace: 'chat', settingName: 'text' }, // irrevocable setting added\r\n            ]),\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should not include irrevocable settings that are already granted', async () => {\r\n      // Mock getUserSettings to return an existing irrevocable setting that is already granted\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([\r\n        {\r\n          namespace: 'chat',\r\n          settingName: 'text',\r\n          effectiveValue: true,\r\n        } as UserSettingValueDTO,\r\n      ]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            settings: [\r\n              { namespace: 'chat', settingName: 'voice' },\r\n              // irrevocable setting 'text' should not be included since it's already granted\r\n            ],\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should include irrevocable settings that exist but are not granted (effectiveValue false)', async () => {\r\n      // Mock getUserSettings to return an existing irrevocable setting that is not granted\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([\r\n        {\r\n          namespace: 'chat',\r\n          settingName: 'text',\r\n          effectiveValue: false,\r\n        } as UserSettingValueDTO,\r\n      ]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            settings: expect.arrayContaining([\r\n              { namespace: 'chat', settingName: 'voice' },\r\n              { namespace: 'chat', settingName: 'text' }, // irrevocable setting included since not granted\r\n            ]),\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should handle when no irrevocable settings exist', async () => {\r\n      // Mock with no irrevocable settings\r\n      jest.spyOn(settingsService, 'getProductSettingsDefinition').mockResolvedValueOnce([\r\n        {\r\n          version: 1,\r\n          orgId: 'test-org-id',\r\n          productId: 'test-product-id',\r\n          namespace: 'chat',\r\n          settings: [\r\n            {\r\n              settingName: 'voice',\r\n              valueType: 'boolean',\r\n              userHidden: false,\r\n              required: false,\r\n              autoReviewConsent: false,\r\n              label: { en: 'Voice Chat' },\r\n              parentNotice: { en: 'Allow voice chat' },\r\n              userNotice: { en: 'Voice chat permission' },\r\n              regions: [],\r\n              irrevocable: false,\r\n            },\r\n          ],\r\n        },\r\n      ]);\r\n\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            settings: [\r\n              { namespace: 'chat', settingName: 'voice' },\r\n              // No irrevocable settings should be added\r\n            ],\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should include existing non-irrevocable settings that are not in the initial request', async () => {\r\n      // Mock getUserSettings to return existing non-irrevocable settings\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([\r\n        {\r\n          namespace: 'social',\r\n          settingName: 'friends',\r\n          effectiveValue: true,\r\n        } as UserSettingValueDTO,\r\n        {\r\n          namespace: 'default',\r\n          settingName: 'analytics',\r\n          effectiveValue: false,\r\n        } as UserSettingValueDTO,\r\n      ]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            settings: expect.arrayContaining([\r\n              { namespace: 'chat', settingName: 'voice' }, // Original requested permission\r\n              { namespace: 'chat', settingName: 'text' }, // Irrevocable setting added\r\n              { namespace: 'social', settingName: 'friends' }, // Existing non-irrevocable setting added\r\n              { namespace: 'default', settingName: 'analytics' }, // Existing non-irrevocable setting added\r\n            ]),\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should not duplicate settings that are already in the request', async () => {\r\n      // Mock getUserSettings to return existing settings where one matches the requested permission\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([\r\n        {\r\n          namespace: 'chat',\r\n          settingName: 'voice', // This matches the requested permission\r\n          effectiveValue: true,\r\n        } as UserSettingValueDTO,\r\n        {\r\n          namespace: 'social',\r\n          settingName: 'friends',\r\n          effectiveValue: false,\r\n        } as UserSettingValueDTO,\r\n      ]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      // Verify that voice setting is not duplicated\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            settings: expect.arrayContaining([\r\n              { namespace: 'chat', settingName: 'voice' }, // Should appear only once\r\n              { namespace: 'chat', settingName: 'text' }, // Irrevocable setting\r\n              { namespace: 'social', settingName: 'friends' }, // Existing non-irrevocable setting\r\n            ]),\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n\r\n      const mockCall = (mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel as jest.Mock).mock\r\n        .calls[0];\r\n      const settingsInRequest = mockCall[0].body.settings;\r\n\r\n      const voiceSettingCount = settingsInRequest.filter(\r\n        (setting: SettingIdentifierDTO) => setting.namespace === 'chat' && setting.settingName === 'voice',\r\n      ).length;\r\n      expect(voiceSettingCount).toBe(1); // Should only appear once, not duplicated\r\n    });\r\n\r\n    it('should include all existing non-irrevocable settings when using settings parameter instead of permissions', async () => {\r\n      // Mock getUserSettings to return existing non-irrevocable settings\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([\r\n        {\r\n          namespace: 'social',\r\n          settingName: 'friends',\r\n          effectiveValue: true,\r\n        } as UserSettingValueDTO,\r\n        {\r\n          namespace: 'chat',\r\n          settingName: 'text', // This is irrevocable and should not be duplicated\r\n          effectiveValue: false,\r\n        } as UserSettingValueDTO,\r\n        {\r\n          namespace: 'default',\r\n          settingName: 'analytics',\r\n          effectiveValue: false,\r\n        } as UserSettingValueDTO,\r\n      ]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          settings: [{ namespace: 'chat', settingName: 'voice' }],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            settings: expect.arrayContaining([\r\n              { namespace: 'chat', settingName: 'voice' }, // Original requested setting\r\n              { namespace: 'chat', settingName: 'text' }, // Irrevocable setting added (should appear only once)\r\n              { namespace: 'social', settingName: 'friends' }, // Existing non-irrevocable setting added\r\n              { namespace: 'default', settingName: 'analytics' }, // Existing non-irrevocable setting added\r\n            ]),\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should handle mixed scenario with existing irrevocable and non-irrevocable settings', async () => {\r\n      // Mock getUserSettings to return a mix of irrevocable and non-irrevocable settings\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([\r\n        {\r\n          namespace: 'chat',\r\n          settingName: 'text', // This is irrevocable\r\n          effectiveValue: false,\r\n        } as UserSettingValueDTO,\r\n        {\r\n          namespace: 'social',\r\n          settingName: 'friends', // This is non-irrevocable\r\n          effectiveValue: true,\r\n        } as UserSettingValueDTO,\r\n        {\r\n          namespace: 'default',\r\n          settingName: 'analytics', // This is non-irrevocable\r\n          effectiveValue: false,\r\n        } as UserSettingValueDTO,\r\n      ]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            settings: expect.arrayContaining([\r\n              { namespace: 'chat', settingName: 'voice' }, // Original requested permission\r\n              { namespace: 'chat', settingName: 'text' }, // Irrevocable setting (added via irrevocable logic, not via existing non-irrevocable logic)\r\n              { namespace: 'social', settingName: 'friends' }, // Existing non-irrevocable setting\r\n              { namespace: 'default', settingName: 'analytics' }, // Existing non-irrevocable setting\r\n            ]),\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n\r\n      const mockCall = (mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel as jest.Mock).mock\r\n        .calls[0];\r\n      const settingsInRequest = mockCall[0].body.settings;\r\n\r\n      // Ensure 'chat.text' appears only once\r\n      const textSettingCount = settingsInRequest.filter(\r\n        (setting: SettingIdentifierDTO) => setting.namespace === 'chat' && setting.settingName === 'text',\r\n      ).length;\r\n      expect(textSettingCount).toBe(1);\r\n    });\r\n\r\n    it('should handle empty existing settings gracefully', async () => {\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            settings: [\r\n              { namespace: 'chat', settingName: 'voice' }, // Original requested permission\r\n              { namespace: 'chat', settingName: 'text' }, // Irrevocable setting\r\n            ],\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should set age to 0 when dob is empty string', async () => {\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '',\r\n          parentEmail: '<EMAIL>',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            age: 0,\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should set age to 0 when dob is undefined', async () => {\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: undefined,\r\n          parentEmail: '<EMAIL>',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            age: 0,\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should not set age when dob has a value', async () => {\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          permissions: ['chat.voice'],\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            age: undefined,\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should deduplicate settings in the request when using explicit settings array', async () => {\r\n      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      const settingsWithDuplicates = [\r\n        { namespace: 'chat', settingName: 'voice' },\r\n        { namespace: 'default', settingName: 'analytics' },\r\n        { namespace: 'chat', settingName: 'voice' }, // Duplicate\r\n        { namespace: 'social', settingName: 'friends' },\r\n        { namespace: 'default', settingName: 'analytics' }, // Duplicate\r\n        { namespace: 'chat', settingName: 'voice' }, // Another duplicate\r\n      ];\r\n\r\n      await settingsService.sendConsentEmail(\r\n        {\r\n          userId: 1,\r\n          productId: '1',\r\n          dob: '2010-10-10',\r\n          parentEmail: '<EMAIL>',\r\n          language: 'ca',\r\n          location: 'US',\r\n          settings: settingsWithDuplicates,\r\n        },\r\n        {\r\n          clientId: '',\r\n          secret: '',\r\n        },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          body: expect.objectContaining({\r\n            settings: [\r\n              { namespace: 'chat', settingName: 'voice' },\r\n              { namespace: 'default', settingName: 'analytics' },\r\n              { namespace: 'social', settingName: 'friends' },\r\n              { namespace: 'chat', settingName: 'text' }, // Added irrevocable setting\r\n            ],\r\n          }),\r\n        }),\r\n        expect.any(Object),\r\n      );\r\n    });\r\n\r\n    it('should throw error when email domain validation fails', async () => {\r\n      jest.spyOn(emailMessageService, 'validateEmailDomain').mockResolvedValue(false);\r\n      await expect(\r\n        settingsService.sendConsentEmail(\r\n          {\r\n            userId: 1,\r\n            productId: '1',\r\n            dob: '2010-10-10',\r\n            parentEmail: '<EMAIL>',\r\n            language: 'ca',\r\n            location: 'US',\r\n            settings: [{ namespace: 'chat', settingName: 'voice' }],\r\n          },\r\n          {\r\n            clientId: '',\r\n            secret: '',\r\n          },\r\n        ),\r\n      ).rejects.toThrow(BadRequestException);\r\n    });\r\n  });\r\n\r\n  describe('getUserSettings', () => {\r\n    beforeEach(() => {\r\n      jest.clearAllMocks();\r\n      // Reset the getUserSettings spy to use the real implementation for these tests\r\n      jest.spyOn(settingsService, 'getUserSettings').mockRestore();\r\n    });\r\n\r\n    it('should return user settings', async () => {\r\n      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await expect(\r\n        settingsService.getUserSettings({ userId: 1, productId: '' }, { clientId: '', secret: '' }),\r\n      ).resolves.toEqual([]);\r\n    });\r\n\r\n    it('should convert unsupported country code to ZZ when getting user settings', async () => {\r\n      const mockGetSettingsForUserAtProductLevel =\r\n        mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel;\r\n      mockGetSettingsForUserAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.getUserSettings(\r\n        { userId: 1, productId: '', location: 'unsupported-country-code' as Tiso31662 },\r\n        { clientId: '', secret: '' },\r\n      );\r\n\r\n      expect(mockGetSettingsForUserAtProductLevel).toHaveBeenCalledWith(\r\n        {\r\n          params: {\r\n            userId: '1',\r\n            productId: '',\r\n          },\r\n          query: {\r\n            format: EResponseFormat.FULL,\r\n            location: 'ZZ',\r\n            age: 0,\r\n          },\r\n        },\r\n        {\r\n          headers: expect.any(Object),\r\n        },\r\n      );\r\n    });\r\n\r\n    it('should should not send undefined', async () => {\r\n      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await expect(\r\n        settingsService.getUserSettings(\r\n          { userId: 1, productId: '', dateOfBirth: undefined, location: 'AD' },\r\n          { clientId: '', secret: '' },\r\n        ),\r\n      ).resolves.toEqual([]);\r\n      expect(mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel).toHaveBeenCalledWith(\r\n        {\r\n          params: {\r\n            userId: '1',\r\n            productId: '',\r\n          },\r\n          query: {\r\n            location: 'AD',\r\n            format: EResponseFormat.FULL,\r\n            age: 0,\r\n          },\r\n        },\r\n        { headers: expect.anything() },\r\n      );\r\n    });\r\n\r\n    it('should return empty array when user settings not found (404) Axios error', async () => {\r\n      const error = new AxiosError('Not Found', '404');\r\n      error.response = { data: { error: { statusCode: 404 } } } as AxiosResponse;\r\n      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockRejectedValueOnce(error);\r\n\r\n      await expect(\r\n        settingsService.getUserSettings(\r\n          { userId: 1, productId: 'test' },\r\n          {\r\n            clientId: '',\r\n            secret: '',\r\n          },\r\n        ),\r\n      ).resolves.toEqual([]);\r\n    });\r\n\r\n    it('should propagate non-404 errors', async () => {\r\n      const error = new AxiosError('Internal Server Error', '500');\r\n      error.response = { data: { error: { statusCode: 500 } } } as AxiosResponse;\r\n      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockRejectedValueOnce(error);\r\n\r\n      await expect(\r\n        settingsService.getUserSettings(\r\n          { userId: 1, productId: 'test' },\r\n          {\r\n            clientId: '',\r\n            secret: '',\r\n          },\r\n        ),\r\n      ).rejects.toThrow('Internal Server Error');\r\n    });\r\n\r\n    it('should set requestAge to 0 when dateOfBirth is empty string', async () => {\r\n      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.getUserSettings(\r\n        { userId: 1, productId: 'test', dateOfBirth: '' },\r\n        { clientId: '', secret: '' },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel).toHaveBeenCalledWith(\r\n        {\r\n          params: {\r\n            userId: '1',\r\n            productId: 'test',\r\n          },\r\n          query: {\r\n            dob: '',\r\n            format: EResponseFormat.FULL,\r\n            age: 0,\r\n            location: 'ZZ',\r\n          },\r\n        },\r\n        { headers: expect.anything() },\r\n      );\r\n    });\r\n\r\n    it('should set requestAge to 0 when dateOfBirth is undefined', async () => {\r\n      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.getUserSettings(\r\n        { userId: 1, productId: 'test', dateOfBirth: undefined },\r\n        { clientId: '', secret: '' },\r\n      );\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel).toHaveBeenCalledWith(\r\n        {\r\n          params: {\r\n            userId: '1',\r\n            productId: 'test',\r\n          },\r\n          query: {\r\n            format: EResponseFormat.FULL,\r\n            age: 0,\r\n            location: 'ZZ',\r\n          },\r\n        },\r\n        { headers: expect.anything() },\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('removeTermsAndConditionsSetting', () => {\r\n    it('should return empty array when freeKWSSettings is undefined', () => {\r\n      expect(SettingsService.removeTermsAndConditionsSetting()).toEqual([]);\r\n    });\r\n\r\n    it('should return empty for empty array', () => {\r\n      expect(SettingsService.removeTermsAndConditionsSetting([])).toEqual([]);\r\n    });\r\n\r\n    it('should return empty if the only setting is terms and conditions', () => {\r\n      expect(\r\n        SettingsService.removeTermsAndConditionsSetting([\r\n          {\r\n            namespace: 'terms',\r\n            settingName: 'app-terms-and-conditions',\r\n            preferredValue: true,\r\n            preferredValueFromOrgLevel: false,\r\n            effectiveValue: true,\r\n            isOrgLevel: false,\r\n            effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,\r\n            definition: {\r\n              ageBracket: {\r\n                consentType: ESettingConsentType.OPT_OUT,\r\n                defaultPreference: '',\r\n              },\r\n              orgId: '',\r\n              namespace: '',\r\n              settingName: '',\r\n              valueType: ESettingValueType.BOOLEAN,\r\n              translations: {},\r\n              restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,\r\n              userHidden: false,\r\n              userReadOnly: false,\r\n              required: false,\r\n            },\r\n          },\r\n        ] as UserSettingValueDTO[]),\r\n      ).toEqual([]);\r\n    });\r\n\r\n    it('should return settings and skip terms and conditions', () => {\r\n      const otherSetting: UserSettingValueDTO = {\r\n        namespace: 'default',\r\n        settingName: 'other setting',\r\n        preferredValue: true,\r\n        preferredValueFromOrgLevel: false,\r\n        effectiveValue: true,\r\n        isOrgLevel: false,\r\n        effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,\r\n        definition: {\r\n          ageBracket: {\r\n            consentType: ESettingConsentType.OPT_OUT,\r\n            defaultPreference: '',\r\n          },\r\n          orgId: '',\r\n          namespace: '',\r\n          settingName: '',\r\n          valueType: ESettingValueType.BOOLEAN,\r\n          translations: {},\r\n          restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,\r\n          userHidden: false,\r\n          userReadOnly: false,\r\n          required: false,\r\n        },\r\n      };\r\n\r\n      expect(\r\n        SettingsService.removeTermsAndConditionsSetting([\r\n          {\r\n            namespace: 'terms',\r\n            settingName: 'app-terms-and-conditions',\r\n            preferredValue: true,\r\n            preferredValueFromOrgLevel: false,\r\n            effectiveValue: true,\r\n            isOrgLevel: false,\r\n            effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,\r\n            definition: {\r\n              ageBracket: {\r\n                consentType: ESettingConsentType.OPT_OUT,\r\n                defaultPreference: '',\r\n              },\r\n              orgId: '',\r\n              namespace: '',\r\n              settingName: '',\r\n              valueType: ESettingValueType.BOOLEAN,\r\n              translations: {},\r\n              restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,\r\n              userHidden: false,\r\n              userReadOnly: false,\r\n              required: false,\r\n            },\r\n          },\r\n          otherSetting,\r\n        ] as UserSettingValueDTO[]),\r\n      ).toEqual([otherSetting]);\r\n    });\r\n  });\r\n\r\n  describe('isOptIn', () => {\r\n    it('should return false for empty setting', () => {\r\n      expect(SettingsService.isOptIn()).toBeFalsy();\r\n    });\r\n\r\n    it('should return true for verified opt-in setting', () => {\r\n      expect(\r\n        SettingsService.isOptIn({\r\n          definition: {\r\n            ageBracket: {\r\n              consentType: ESettingConsentType.OPT_IN_VERIFIED,\r\n            },\r\n          },\r\n        } as UserSettingValueDTO),\r\n      ).toBeTruthy();\r\n    });\r\n\r\n    it('should return true for unverified opt-in setting', () => {\r\n      expect(\r\n        SettingsService.isOptIn({\r\n          definition: {\r\n            ageBracket: {\r\n              consentType: ESettingConsentType.OPT_IN_UNVERIFIED,\r\n            },\r\n          },\r\n        } as UserSettingValueDTO),\r\n      ).toBeTruthy();\r\n    });\r\n\r\n    it('should return false for opt-out setting', () => {\r\n      expect(\r\n        SettingsService.isOptIn({\r\n          definition: {\r\n            ageBracket: {\r\n              consentType: ESettingConsentType.OPT_OUT,\r\n            },\r\n          },\r\n        } as UserSettingValueDTO),\r\n      ).toBeFalsy();\r\n    });\r\n  });\r\n\r\n  describe('isOptOut', () => {\r\n    it('should return false for empty setting', () => {\r\n      expect(SettingsService.isOptOut(null as unknown as UserSettingValueDTO)).toBeFalsy();\r\n    });\r\n\r\n    it('should return true for opt-out setting', () => {\r\n      expect(\r\n        SettingsService.isOptOut({\r\n          definition: {\r\n            ageBracket: {\r\n              consentType: ESettingConsentType.OPT_OUT,\r\n            },\r\n          },\r\n        } as UserSettingValueDTO),\r\n      ).toBeTruthy();\r\n    });\r\n\r\n    it('should return true for opt-in setting', () => {\r\n      expect(\r\n        SettingsService.isOptOut({\r\n          definition: {\r\n            ageBracket: {\r\n              consentType: ESettingConsentType.OPT_IN_UNVERIFIED,\r\n            },\r\n          },\r\n        } as UserSettingValueDTO),\r\n      ).toBeFalsy();\r\n    });\r\n  });\r\n\r\n  describe('transformSettingsToPermissions', () => {\r\n    it('should return mapped permissions', () => {\r\n      expect(\r\n        SettingsService.transformSettingsToPermissions(\r\n          [\r\n            {\r\n              namespace: 'chat',\r\n              settingName: 'voice',\r\n              preferredValue: true,\r\n              preferredValueFromOrgLevel: false,\r\n              effectiveValue: true,\r\n              isOrgLevel: false,\r\n              effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,\r\n              definition: {\r\n                ageBracket: {\r\n                  consentType: ESettingConsentType.OPT_OUT,\r\n                  defaultPreference: '',\r\n                },\r\n                orgId: '',\r\n                namespace: '',\r\n                settingName: '',\r\n                valueType: ESettingValueType.BOOLEAN,\r\n                translations: {},\r\n                restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,\r\n                userHidden: false,\r\n                userReadOnly: false,\r\n                required: false,\r\n              },\r\n            },\r\n          ],\r\n          ['chat.voice', 'default.whatever'],\r\n        ),\r\n      ).toEqual({\r\n        'chat.voice': true,\r\n        'default.whatever': null,\r\n      });\r\n    });\r\n\r\n    it('should return permissions without namespace', () => {\r\n      expect(\r\n        SettingsService.transformSettingsToPermissions(\r\n          [\r\n            {\r\n              namespace: 'chat',\r\n              settingName: 'voice',\r\n              preferredValue: true,\r\n              preferredValueFromOrgLevel: false,\r\n              effectiveValue: true,\r\n              isOrgLevel: false,\r\n              effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,\r\n              definition: {\r\n                ageBracket: {\r\n                  consentType: ESettingConsentType.OPT_OUT,\r\n                  defaultPreference: '',\r\n                },\r\n                orgId: '',\r\n                namespace: '',\r\n                settingName: '',\r\n                valueType: ESettingValueType.BOOLEAN,\r\n                translations: {},\r\n                restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,\r\n                userHidden: false,\r\n                userReadOnly: false,\r\n                required: false,\r\n              },\r\n            },\r\n            {\r\n              namespace: 'default',\r\n              settingName: 'whatever',\r\n              preferredValue: true,\r\n              preferredValueFromOrgLevel: false,\r\n              effectiveValue: true,\r\n              isOrgLevel: false,\r\n              effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,\r\n              definition: {\r\n                ageBracket: {\r\n                  consentType: ESettingConsentType.OPT_OUT,\r\n                  defaultPreference: '',\r\n                },\r\n                orgId: '',\r\n                namespace: '',\r\n                settingName: '',\r\n                valueType: ESettingValueType.BOOLEAN,\r\n                translations: {},\r\n                restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,\r\n                userHidden: false,\r\n                userReadOnly: false,\r\n                required: false,\r\n              },\r\n            },\r\n          ],\r\n          ['chat.voice', 'whatever'],\r\n        ),\r\n      ).toEqual({\r\n        'chat.voice': true,\r\n        whatever: true,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('transformSettingsToGroupedPermissions', () => {\r\n    it('should throw an error if some permission is not found', () => {\r\n      try {\r\n        SettingsService.transformSettingsToGroupedPermissions([], ['chat.voice']);\r\n      } catch (error) {\r\n        expect(error).toBeInstanceOf(NotFoundException);\r\n      }\r\n    });\r\n\r\n    it('should return mapped result permission', () => {\r\n      expect(\r\n        SettingsService.transformSettingsToGroupedPermissions(\r\n          [\r\n            {\r\n              namespace: 'chat',\r\n              settingName: 'voice',\r\n              preferredValue: true,\r\n              preferredValueFromOrgLevel: false,\r\n              effectiveValue: true,\r\n              isOrgLevel: false,\r\n              definition: {\r\n                ageBracket: {\r\n                  consentType: ESettingConsentType.OPT_IN_VERIFIED,\r\n                },\r\n                translations: {\r\n                  en: {\r\n                    label: 'Voice chat',\r\n                    parentNotice: 'Allow user to use voice chat',\r\n                  },\r\n                },\r\n              },\r\n            } as unknown as UserSettingValueDTO,\r\n            {\r\n              namespace: 'chat',\r\n              settingName: 'text',\r\n              preferredValue: true,\r\n              preferredValueFromOrgLevel: false,\r\n              effectiveValue: false,\r\n              isOrgLevel: false,\r\n              definition: {\r\n                ageBracket: {\r\n                  consentType: ESettingConsentType.OPT_IN_VERIFIED,\r\n                },\r\n                translations: {\r\n                  en: {\r\n                    label: 'Text chat',\r\n                    parentNotice: 'Allow user to use text chat',\r\n                  },\r\n                },\r\n              },\r\n            } as unknown as UserSettingValueDTO,\r\n            {\r\n              namespace: 'default',\r\n              settingName: 'whatever',\r\n              preferredValue: true,\r\n              preferredValueFromOrgLevel: false,\r\n              effectiveValue: true,\r\n              isOrgLevel: false,\r\n              definition: {\r\n                ageBracket: {\r\n                  consentType: ESettingConsentType.OPT_OUT,\r\n                },\r\n                translations: {\r\n                  en: {\r\n                    label: 'Default permission',\r\n                    parentNotice: 'Allow user to do everything',\r\n                  },\r\n                },\r\n              },\r\n            } as unknown as UserSettingValueDTO,\r\n          ],\r\n          ['chat.voice', 'chat.text', 'default.whatever'],\r\n        ),\r\n      ).toEqual({\r\n        alreadyGrantedPerms: [\r\n          {\r\n            description: 'Allow user to do everything',\r\n            displayName: 'Default permission',\r\n            name: 'whatever',\r\n          },\r\n        ],\r\n        automaticallyGrantedPerms: [],\r\n        missingPermissions: expect.arrayContaining([\r\n          {\r\n            description: 'Allow user to use text chat',\r\n            displayName: 'Text chat',\r\n            name: 'chat.text',\r\n          },\r\n          {\r\n            description: 'Allow user to use voice chat',\r\n            displayName: 'Voice chat',\r\n            name: 'chat.voice',\r\n          },\r\n        ]),\r\n      });\r\n    });\r\n\r\n    it('should categorize opt-out settings as automatically granted when not enabled', () => {\r\n      expect(\r\n        SettingsService.transformSettingsToGroupedPermissions(\r\n          [\r\n            {\r\n              namespace: 'default',\r\n              settingName: 'optout-setting',\r\n              preferredValue: false,\r\n              preferredValueFromOrgLevel: false,\r\n              effectiveValue: false,\r\n              isOrgLevel: false,\r\n              parentLimitUpdatedAt: new Date(),\r\n              definition: {\r\n                ageBracket: {\r\n                  consentType: ESettingConsentType.OPT_OUT,\r\n                },\r\n                translations: {\r\n                  en: {\r\n                    label: 'Opt-out Setting',\r\n                    parentNotice: 'Opt-out setting description',\r\n                  },\r\n                },\r\n              },\r\n            } as unknown as UserSettingValueDTO,\r\n          ],\r\n          ['optout-setting'],\r\n        ),\r\n      ).toEqual({\r\n        alreadyGrantedPerms: [],\r\n        automaticallyGrantedPerms: [\r\n          {\r\n            description: 'Opt-out setting description',\r\n            displayName: 'Opt-out Setting',\r\n            name: 'optout-setting',\r\n          },\r\n        ],\r\n        missingPermissions: [],\r\n      });\r\n    });\r\n  });\r\n\r\n  it('should throw error when translation not found for language', () => {\r\n    const setting = {\r\n      namespace: 'default',\r\n      settingName: 'test-setting',\r\n      preferredValue: false,\r\n      preferredValueFromOrgLevel: false,\r\n      effectiveValue: false,\r\n      isOrgLevel: false,\r\n      parentLimitUpdatedAt: new Date(),\r\n      definition: {\r\n        ageBracket: {\r\n          consentType: ESettingConsentType.OPT_OUT,\r\n        },\r\n        translations: {\r\n          en: {\r\n            label: 'Test Setting',\r\n            parentNotice: 'Test description',\r\n          },\r\n        },\r\n      },\r\n    } as unknown as UserSettingValueDTO;\r\n\r\n    expect(() => {\r\n      SettingsService.transformSettingsToGroupedPermissions([setting], ['test-setting'], 'fr');\r\n    }).toThrow('Translation not found for fr');\r\n  });\r\n\r\n  describe('resendConsentEmail', () => {\r\n    beforeEach(() => {\r\n      // Reset the getUserSettings spy to use the real implementation for these tests\r\n      jest.spyOn(settingsService, 'getUserSettings').mockRestore();\r\n    });\r\n    it('should return revoked permissions', async () => {\r\n      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            settings: [\r\n              {\r\n                namespace: 'chat',\r\n                settingName: 'voice',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: true,\r\n                isOrgLevel: false,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_IN_VERIFIED,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Voice chat',\r\n                      parentNotice: 'Allow user to use voice chat',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n              {\r\n                namespace: 'chat',\r\n                settingName: 'text',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: false,\r\n                isOrgLevel: false,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_IN_VERIFIED,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Text chat',\r\n                      parentNotice: 'Allow user to use text chat',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n              {\r\n                namespace: 'default',\r\n                settingName: 'whatever',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: true,\r\n                isOrgLevel: false,\r\n                consentRequestedAt: 1784393974,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_OUT,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Default permission',\r\n                      parentNotice: 'Allow user to do everything',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n            ],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [\r\n              {\r\n                consentId: '',\r\n                consentName: '',\r\n              },\r\n            ],\r\n            settings: [\r\n              {\r\n                namespace: 'default',\r\n                settingName: 'whatever',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: true,\r\n                isOrgLevel: false,\r\n                consentRequestedAt: 1784393974,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_OUT,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Default permission',\r\n                      parentNotice: 'Allow user to do everything',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n            ],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await expect(\r\n        settingsService.resendConsentEmail(\r\n          { userId: 1, productId: '1', parentEmail: 'email' },\r\n          { clientId: '', secret: '' },\r\n        ),\r\n      ).resolves.toEqual(undefined);\r\n    });\r\n\r\n    it('should convert unsupported country code to ZZ in resend consent request', async () => {\r\n      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            settings: [\r\n              {\r\n                namespace: 'default',\r\n                settingName: 'whatever',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: true,\r\n                isOrgLevel: false,\r\n                consentRequestedAt: 1784393974,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_OUT,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Default permission',\r\n                      parentNotice: 'Allow user to do everything',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n            ],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      const sendConsentEmailAtProductLevel =\r\n        mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel;\r\n      sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [\r\n              {\r\n                consentId: 'id',\r\n                consentName: 'name',\r\n              },\r\n            ],\r\n            settings: [],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await settingsService.resendConsentEmail(\r\n        { userId: 1, productId: '1', parentEmail: 'email', location: 'unsupported-country-code' as Tiso31662 },\r\n        { clientId: '', secret: '' },\r\n      );\r\n\r\n      expect(sendConsentEmailAtProductLevel).toHaveBeenCalledWith(\r\n        {\r\n          params: expect.any(Object),\r\n          body: {\r\n            parentEmail: expect.any(String),\r\n            language: undefined,\r\n            location: 'ZZ',\r\n            dob: undefined,\r\n            settings: expect.any(Array),\r\n          },\r\n        },\r\n        {\r\n          headers: expect.any(Object),\r\n        },\r\n      );\r\n    });\r\n\r\n    it('should throw an error if consent not requested', async () => {\r\n      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            settings: [\r\n              {\r\n                namespace: 'chat',\r\n                settingName: 'voice',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: true,\r\n                isOrgLevel: false,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_IN_VERIFIED,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Voice chat',\r\n                      parentNotice: 'Allow user to use voice chat',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n              {\r\n                namespace: 'chat',\r\n                settingName: 'text',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: false,\r\n                isOrgLevel: false,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_IN_VERIFIED,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Text chat',\r\n                      parentNotice: 'Allow user to use text chat',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n              {\r\n                namespace: 'default',\r\n                settingName: 'whatever',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: true,\r\n                isOrgLevel: false,\r\n                consentRequestedAt: 1784393974,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_OUT,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Default permission',\r\n                      parentNotice: 'Allow user to do everything',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n            ],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            consentRequestId: '',\r\n            consents: [],\r\n            settings: [\r\n              {\r\n                namespace: 'chat',\r\n                settingName: 'voice',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: true,\r\n                effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,\r\n                isOrgLevel: false,\r\n              } as UserSettingValueShortDTO,\r\n            ],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      await expect(\r\n        settingsService.resendConsentEmail(\r\n          { userId: 1, productId: '1', parentEmail: 'email' },\r\n          { clientId: '', secret: '' },\r\n        ),\r\n      ).rejects.toThrow(SettingsServiceConsentNotRequestedError);\r\n    });\r\n\r\n    it('should throw an error if parent not a part of user family', async () => {\r\n      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({\r\n        data: {\r\n          response: {\r\n            settings: [\r\n              {\r\n                namespace: 'chat',\r\n                settingName: 'voice',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: true,\r\n                isOrgLevel: false,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_IN_VERIFIED,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Voice chat',\r\n                      parentNotice: 'Allow user to use voice chat',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n              {\r\n                namespace: 'chat',\r\n                settingName: 'text',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: false,\r\n                isOrgLevel: false,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_IN_VERIFIED,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Text chat',\r\n                      parentNotice: 'Allow user to use text chat',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n              {\r\n                namespace: 'default',\r\n                settingName: 'whatever',\r\n                preferredValue: true,\r\n                preferredValueFromOrgLevel: false,\r\n                effectiveValue: true,\r\n                isOrgLevel: false,\r\n                consentRequestedAt: 1784393974,\r\n                definition: {\r\n                  ageBracket: {\r\n                    consentType: ESettingConsentType.OPT_OUT,\r\n                  },\r\n                  translations: {\r\n                    en: {\r\n                      label: 'Default permission',\r\n                      parentNotice: 'Allow user to do everything',\r\n                    },\r\n                  },\r\n                },\r\n              } as unknown as UserSettingValueDTO,\r\n            ],\r\n          },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      });\r\n\r\n      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockRejectedValueOnce(\r\n        new AxiosError<SettingsErrorResponse>(\r\n          'ERR_BAD_REQUEST',\r\n          '400',\r\n          {},\r\n          {},\r\n          {\r\n            data: {\r\n              error: {\r\n                statusCode: 400,\r\n                code: '',\r\n                errorCode: ESettingsServiceErrorCodes.CONSENT_REQUEST_PARENT_NOT_PART_OF_CHILD_FAMILY,\r\n                message: '',\r\n              },\r\n              meta: {\r\n                requestId: '',\r\n                timestamp: new Date().toISOString(),\r\n              },\r\n            },\r\n            status: 400,\r\n            statusText: 'ERR_BAD_REQUEST',\r\n            headers: {},\r\n            config: {},\r\n          },\r\n        ),\r\n      );\r\n\r\n      await expect(\r\n        settingsService.resendConsentEmail(\r\n          { userId: 1, productId: '1', parentEmail: 'email' },\r\n          { clientId: '', secret: '' },\r\n        ),\r\n      ).rejects.toThrow(SettingsServiceParentNotInFamilyError);\r\n    });\r\n  });\r\n\r\n  describe('transformSettingsToAllPermissions', () => {\r\n    it('maps permission value to null when value not set by parent and effective value is false', () => {\r\n      const permissions = SettingsService.transformSettingsToAllPermissions([\r\n        {\r\n          namespace: 'chat',\r\n          settingName: 'voice',\r\n          effectiveValue: false,\r\n          parentLimitUpdatedAt: undefined,\r\n        } as UserSettingValueDTO,\r\n      ]);\r\n      expect(permissions).toEqual({\r\n        'chat.voice': null,\r\n      });\r\n    });\r\n\r\n    it('maps permission value to true', () => {\r\n      const permissions = SettingsService.transformSettingsToAllPermissions([\r\n        {\r\n          namespace: 'chat',\r\n          settingName: 'voice',\r\n          effectiveValue: true,\r\n          parentLimitUpdatedAt: 1,\r\n          definition: optInDefinition,\r\n        } as UserSettingValueDTO,\r\n      ]);\r\n      expect(permissions).toEqual({\r\n        'chat.voice': true,\r\n      });\r\n    });\r\n\r\n    it('maps permission value to false', () => {\r\n      const permissions = SettingsService.transformSettingsToAllPermissions([\r\n        {\r\n          namespace: 'chat',\r\n          settingName: 'voice',\r\n          effectiveValue: false,\r\n          parentLimitUpdatedAt: 1,\r\n          definition: optInDefinition,\r\n        } as UserSettingValueDTO,\r\n      ]);\r\n      expect(permissions).toEqual({\r\n        'chat.voice': false,\r\n      });\r\n    });\r\n\r\n    it('omits namespace when namespace is default', () => {\r\n      const permissions = SettingsService.transformSettingsToAllPermissions([\r\n        {\r\n          namespace: 'default',\r\n          settingName: 'voice',\r\n          effectiveValue: false,\r\n          parentLimitUpdatedAt: 1,\r\n          definition: optInDefinition,\r\n        } as UserSettingValueDTO,\r\n      ]);\r\n      expect(permissions).toEqual({\r\n        voice: false,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('sendGenerateConsentRequest', () => {\r\n    it('should trigger generation of a consent request', async () => {\r\n      const userId = 1;\r\n      const productId = '2';\r\n\r\n      const generatedConsentRequest = jest.fn();\r\n      mockSettingsApi.getModule('userSettingValue').generateConsentRequestAtProductLevelRequest =\r\n        generatedConsentRequest;\r\n\r\n      await settingsService.sendGenerateConsentRequest(userId, productId, {\r\n        clientId: 'client-id',\r\n        secret: 'top-secret',\r\n      });\r\n\r\n      expect(generatedConsentRequest).toHaveBeenCalledWith(\r\n        {\r\n          params: {\r\n            userId: `${userId}`,\r\n            productId: `${productId}`,\r\n          },\r\n        },\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${TOKEN}`,\r\n          },\r\n        },\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('getProductSettingsDefinition', () => {\r\n    beforeEach(() => {\r\n      jest.spyOn(settingsService, 'getProductSettingsDefinition').mockRestore();\r\n    });\r\n\r\n    it('should return product settings definition', async () => {\r\n      const mockYamlDefinition = `version: 1\r\norgId: \"test-org-id\"\r\nproductId: \"test-product-id\"\r\nnamespace: \"chat\"\r\nsettings:\r\n  - settingName: \"voice\"\r\n    valueType: \"boolean\"`;\r\n\r\n      const mockResponse = {\r\n        data: {\r\n          response: {\r\n            definitions: [mockYamlDefinition],\r\n          },\r\n          meta: {\r\n            requestId: 'test-request-id',\r\n            timestamp: new Date().toISOString(),\r\n          },\r\n        },\r\n        status: 200,\r\n      };\r\n\r\n      jest.spyOn(AuthLibraryUtils, 'isTokenValid').mockReturnValue(false);\r\n      jest.spyOn(settingsService['keycloakService'], 'getScopedAccessToken').mockResolvedValue('new-token');\r\n      mockSettingsApi\r\n        .getModule('setting')\r\n        .getInternalAdminSettingDefintionsYamlAtProductLevel.mockResolvedValueOnce(mockResponse);\r\n\r\n      const result = await settingsService.getProductSettingsDefinition({\r\n        productId: 'test-product-id',\r\n        productEnvId: 'test-product-env-id',\r\n        orgId: 'test-org-id',\r\n        orgEnvId: 'test-org-env-id',\r\n      });\r\n\r\n      expect(settingsService['keycloakService'].getScopedAccessToken).toHaveBeenCalledWith([EKeycloakScope.SETTINGS]);\r\n      expect(\r\n        mockSettingsApi.getModule('setting').getInternalAdminSettingDefintionsYamlAtProductLevel,\r\n      ).toHaveBeenCalledWith(\r\n        {\r\n          params: {\r\n            orgId: 'test-org-id',\r\n            orgEnvId: 'test-org-env-id',\r\n            productId: 'test-product-id',\r\n            productEnvId: 'test-product-env-id',\r\n          },\r\n          query: {},\r\n        },\r\n        {\r\n          headers: {\r\n            Authorization: 'Bearer new-token',\r\n          },\r\n        },\r\n      );\r\n\r\n      expect(result).toHaveLength(1);\r\n      expect(result[0]).toEqual({\r\n        version: 1,\r\n        orgId: 'test-org-id',\r\n        productId: 'test-product-id',\r\n        namespace: 'chat',\r\n        settings: [\r\n          {\r\n            settingName: 'voice',\r\n            valueType: 'boolean',\r\n          },\r\n        ],\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('country codes', () => {\r\n    test.each(['JA', null, undefined, '', ' '])(\r\n      'unsupported codes should be converted to ZZ',\r\n      async (countryCode?: string) => {\r\n        expect(SettingsService.convertUnsupportedCountryCode(countryCode as Tiso31662)).toBe('ZZ');\r\n      },\r\n    );\r\n\r\n    test.each(['AD', 'GB', 'ES', 'TW', 'ZZ', 'gb'])(\r\n      'supported codes should not be converted',\r\n      async (countryCode?: string) => {\r\n        expect(SettingsService.convertUnsupportedCountryCode(countryCode as Tiso31662)).toBe(countryCode);\r\n      },\r\n    );\r\n  });\r\n\r\n  describe('deleteUserSettingsAtProductLevel', () => {\r\n    it('should delete user settings at product level and return the response', async () => {\r\n      const userId = 123;\r\n      const productId = 'product-123';\r\n      const settings = [\r\n        { namespace: 'chat', settingName: 'voice' },\r\n        { namespace: 'default', settingName: 'whatever' },\r\n      ];\r\n      const mockResponse = {\r\n        data: {\r\n          response: { success: true, settings: [] },\r\n          meta: {\r\n            timestamp: '',\r\n            requestId: '',\r\n          },\r\n        },\r\n        status: 200,\r\n      };\r\n\r\n      mockSettingsApi\r\n        .getModule('userSettingValue')\r\n        .deletePreferredValueRequestAtProductLevel.mockResolvedValueOnce(mockResponse);\r\n\r\n      const result = await settingsService.deleteUserSettingsAtProductLevel(userId, productId, settings, {\r\n        clientId: '',\r\n        secret: '',\r\n      });\r\n\r\n      expect(\r\n        mockSettingsApi.getModule('userSettingValue').deletePreferredValueRequestAtProductLevel,\r\n      ).toHaveBeenCalledWith(\r\n        {\r\n          params: {\r\n            userId: '123',\r\n            productId: 'product-123',\r\n          },\r\n          body: {\r\n            settings,\r\n          },\r\n        },\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${TOKEN}`,\r\n          },\r\n        },\r\n      );\r\n      expect(result).toEqual({ success: true, settings: [] });\r\n    });\r\n\r\n    it('should propagate errors when deleting user settings', async () => {\r\n      const error = new Error('API Error');\r\n      mockSettingsApi\r\n        .getModule('userSettingValue')\r\n        .deletePreferredValueRequestAtProductLevel.mockRejectedValueOnce(error);\r\n\r\n      await expect(\r\n        settingsService.deleteUserSettingsAtProductLevel(123, 'product-123', [], { clientId: '', secret: '' }),\r\n      ).rejects.toThrow('API Error');\r\n    });\r\n  });\r\n\r\n  describe('deleteUser', () => {\r\n    it('should delete a user', async () => {\r\n      const userId = 123;\r\n\r\n      await settingsService.deleteUser(userId, { clientId: '', secret: '' });\r\n\r\n      expect(mockSettingsApi.getModule('userSettingValue').deleteUserRequest).toHaveBeenCalledWith(\r\n        {\r\n          params: {\r\n            userId: '123',\r\n          },\r\n        },\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${TOKEN}`,\r\n          },\r\n        },\r\n      );\r\n    });\r\n\r\n    it('should propagate errors when deleting a user', async () => {\r\n      const error = new Error('API Error');\r\n      mockSettingsApi.getModule('userSettingValue').deleteUserRequest.mockRejectedValueOnce(error);\r\n\r\n      await expect(settingsService.deleteUser(123, { clientId: '', secret: '' })).rejects.toThrow('API Error');\r\n    });\r\n  });\r\n});\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/common/services/settings/settings.service.spec.ts b/src/common/services/settings/settings.service.spec.ts
--- a/src/common/services/settings/settings.service.spec.ts	(revision 0c521299efb5015985a197f21166cc24fbc11eed)
+++ b/src/common/services/settings/settings.service.spec.ts	(date 1755268615435)
@@ -31,14 +31,18 @@
 import { OrgEnv } from '../../../org-env/org-env.entity';
 import { Testing } from '../../utils';
 import { ClientKeycloakService } from '../keycloak/client-keycloak.service';
+import {
+  ok,
+  makeUserSettingValue,
+  makeShortUserSettingValue,
+  makeDefinition,
+  productDefinitionWithChat,
+  optInDefinition as optInDefHelper,
+} from './settings.service.test-utils';
 
 const mockSettingsApi = createClientMock(settingsBackendPlugin, jest.fn);
 
-const optInDefinition = {
-  ageBracket: {
-    consentType: ESettingConsentType.OPT_IN_UNVERIFIED,
-  },
-};
+const optInDefinition = optInDefHelper;
 describe('SettingsService', () => {
   const TOKEN = 'TOKEN';
   let settingsService: SettingsService;
@@ -73,43 +77,7 @@
 
     jest.spyOn(appRepo, 'findOneOrFail').mockResolvedValue(mockApp);
 
-    jest.spyOn(settingsService, 'getProductSettingsDefinition').mockResolvedValue([
-      {
-        version: 1,
-        orgId: 'test-org-id',
-        productId: 'test-product-id',
-        namespace: 'chat',
-        settings: [
-          {
-            settingName: 'voice',
-            valueType: 'boolean',
-            userHidden: false,
-            required: false,
-            autoReviewConsent: false,
-            label: { en: 'Voice Chat' },
-            parentNotice: { en: 'Allow voice chat' },
-            userNotice: { en: 'Voice chat permission' },
-            regions: [],
-            irrevocable: false,
-          },
-          {
-            settingName: 'text',
-            valueType: 'boolean',
-            userHidden: false,
-            required: false,
-            autoReviewConsent: false,
-            label: { en: 'Text Chat' },
-            parentNotice: { en: 'Allow text chat' },
-            userNotice: { en: 'Text chat permission' },
-            regions: [],
-            irrevocable: true,
-          },
-        ],
-      },
-    ]);
-
-    // Mock getUserSettings to return empty array by default
-    jest.spyOn(settingsService, 'getUserSettings').mockResolvedValue([]);
+    // Default mocks kept minimal; product definition mocked per suite.
 
     jest.spyOn(emailMessageService, 'validateEmailDomain').mockResolvedValue(true);
   });
@@ -119,14 +87,19 @@
   });
 
   describe('sendConsentEmail', () => {
+    beforeEach(() => {
+      jest.spyOn(settingsService, 'getProductSettingsDefinition').mockResolvedValue(productDefinitionWithChat());
+      jest.spyOn(settingsService, 'getUserSettings').mockResolvedValue([]);
+    });
     it('should return effective values of consent request', async () => {
-      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .sendConsentEmailAtProductLevel.mockResolvedValueOnce(
+          ok({
             consentRequestId: '',
             consents: [],
             settings: [
-              {
+              makeShortUserSettingValue({
                 namespace: 'chat',
                 settingName: 'voice',
                 preferredValue: true,
@@ -134,16 +107,10 @@
                 effectiveValue: true,
                 effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
                 isOrgLevel: false,
-              } as UserSettingValueShortDTO,
+              }),
             ],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+          }),
+        );
 
       const result = await settingsService.sendConsentEmail(
         {
@@ -211,19 +178,7 @@
     it('should convert unsupported country code to ZZ in consent request', async () => {
       const mockSendConsentEmailAtProductLevel =
         mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel;
-      mockSendConsentEmailAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            consents: [],
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSendConsentEmailAtProductLevel.mockResolvedValueOnce(ok({ consents: [], settings: [] }));
 
       await settingsService.sendConsentEmail(
         {
@@ -323,20 +278,9 @@
       // Mock getUserSettings to return no existing settings
       jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);
 
-      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            consentRequestId: '',
-            consents: [],
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .sendConsentEmailAtProductLevel.mockResolvedValueOnce(ok({ consentRequestId: '', consents: [], settings: [] }));
 
       await settingsService.sendConsentEmail(
         {
@@ -377,20 +321,9 @@
         } as UserSettingValueDTO,
       ]);
 
-      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            consentRequestId: '',
-            consents: [],
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .sendConsentEmailAtProductLevel.mockResolvedValueOnce(ok({ consentRequestId: '', consents: [], settings: [] }));
 
       await settingsService.sendConsentEmail(
         {
@@ -431,20 +364,9 @@
         } as UserSettingValueDTO,
       ]);
 
-      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            consentRequestId: '',
-            consents: [],
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .sendConsentEmailAtProductLevel.mockResolvedValueOnce(ok({ consentRequestId: '', consents: [], settings: [] }));
 
       await settingsService.sendConsentEmail(
         {
@@ -502,20 +424,9 @@
 
       jest.spyOn(settingsService, 'getUserSettings').mockResolvedValueOnce([]);
 
-      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            consentRequestId: '',
-            consents: [],
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .sendConsentEmailAtProductLevel.mockResolvedValueOnce(ok({ consentRequestId: '', consents: [], settings: [] }));
 
       await settingsService.sendConsentEmail(
         {
@@ -561,20 +472,9 @@
         } as UserSettingValueDTO,
       ]);
 
-      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            consentRequestId: '',
-            consents: [],
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .sendConsentEmailAtProductLevel.mockResolvedValueOnce(ok({ consentRequestId: '', consents: [], settings: [] }));
 
       await settingsService.sendConsentEmail(
         {
@@ -622,20 +522,9 @@
         } as UserSettingValueDTO,
       ]);
 
-      mockSettingsApi.getModule('userSettingValue').sendConsentEmailAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            consentRequestId: '',
-            consents: [],
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .sendConsentEmailAtProductLevel.mockResolvedValueOnce(ok({ consentRequestId: '', consents: [], settings: [] }));
 
       await settingsService.sendConsentEmail(
         {
@@ -1080,18 +969,9 @@
     });
 
     it('should return user settings', async () => {
-      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .getSettingsForUserAtProductLevel.mockResolvedValueOnce(ok({ settings: [] }));
 
       await expect(
         settingsService.getUserSettings({ userId: 1, productId: '' }, { clientId: '', secret: '' }),
@@ -1101,18 +981,7 @@
     it('should convert unsupported country code to ZZ when getting user settings', async () => {
       const mockGetSettingsForUserAtProductLevel =
         mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel;
-      mockGetSettingsForUserAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockGetSettingsForUserAtProductLevel.mockResolvedValueOnce(ok({ settings: [] }));
 
       await settingsService.getUserSettings(
         { userId: 1, productId: '', location: 'unsupported-country-code' as Tiso31662 },
@@ -1138,18 +1007,9 @@
     });
 
     it('should should not send undefined', async () => {
-      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .getSettingsForUserAtProductLevel.mockResolvedValueOnce(ok({ settings: [] }));
 
       await expect(
         settingsService.getUserSettings(
@@ -1206,18 +1066,9 @@
     });
 
     it('should set requestAge to 0 when dateOfBirth is empty string', async () => {
-      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .getSettingsForUserAtProductLevel.mockResolvedValueOnce(ok({ settings: [] }));
 
       await settingsService.getUserSettings(
         { userId: 1, productId: 'test', dateOfBirth: '' },
@@ -1242,18 +1093,9 @@
     });
 
     it('should set requestAge to 0 when dateOfBirth is undefined', async () => {
-      mockSettingsApi.getModule('userSettingValue').getSettingsForUserAtProductLevel.mockResolvedValueOnce({
-        data: {
-          response: {
-            settings: [],
-          },
-          meta: {
-            timestamp: '',
-            requestId: '',
-          },
-        },
-        status: 200,
-      });
+      mockSettingsApi
+        .getModule('userSettingValue')
+        .getSettingsForUserAtProductLevel.mockResolvedValueOnce(ok({ settings: [] }));
 
       await settingsService.getUserSettings(
         { userId: 1, productId: 'test', dateOfBirth: undefined },
@@ -1318,30 +1160,7 @@
     });
 
     it('should return settings and skip terms and conditions', () => {
-      const otherSetting: UserSettingValueDTO = {
-        namespace: 'default',
-        settingName: 'other setting',
-        preferredValue: true,
-        preferredValueFromOrgLevel: false,
-        effectiveValue: true,
-        isOrgLevel: false,
-        effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
-        definition: {
-          ageBracket: {
-            consentType: ESettingConsentType.OPT_OUT,
-            defaultPreference: '',
-          },
-          orgId: '',
-          namespace: '',
-          settingName: '',
-          valueType: ESettingValueType.BOOLEAN,
-          translations: {},
-          restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
-          userHidden: false,
-          userReadOnly: false,
-          required: false,
-        },
-      };
+      const otherSetting = makeUserSettingValue({ namespace: 'default', settingName: 'other setting', effectiveValue: true });
 
       expect(
         SettingsService.removeTermsAndConditionsSetting([
@@ -1451,32 +1270,7 @@
     it('should return mapped permissions', () => {
       expect(
         SettingsService.transformSettingsToPermissions(
-          [
-            {
-              namespace: 'chat',
-              settingName: 'voice',
-              preferredValue: true,
-              preferredValueFromOrgLevel: false,
-              effectiveValue: true,
-              isOrgLevel: false,
-              effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
-              definition: {
-                ageBracket: {
-                  consentType: ESettingConsentType.OPT_OUT,
-                  defaultPreference: '',
-                },
-                orgId: '',
-                namespace: '',
-                settingName: '',
-                valueType: ESettingValueType.BOOLEAN,
-                translations: {},
-                restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
-                userHidden: false,
-                userReadOnly: false,
-                required: false,
-              },
-            },
-          ],
+          [makeUserSettingValue({ namespace: 'chat', settingName: 'voice', effectiveValue: true })],
           ['chat.voice', 'default.whatever'],
         ),
       ).toEqual({
@@ -1489,54 +1283,8 @@
       expect(
         SettingsService.transformSettingsToPermissions(
           [
-            {
-              namespace: 'chat',
-              settingName: 'voice',
-              preferredValue: true,
-              preferredValueFromOrgLevel: false,
-              effectiveValue: true,
-              isOrgLevel: false,
-              effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
-              definition: {
-                ageBracket: {
-                  consentType: ESettingConsentType.OPT_OUT,
-                  defaultPreference: '',
-                },
-                orgId: '',
-                namespace: '',
-                settingName: '',
-                valueType: ESettingValueType.BOOLEAN,
-                translations: {},
-                restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
-                userHidden: false,
-                userReadOnly: false,
-                required: false,
-              },
-            },
-            {
-              namespace: 'default',
-              settingName: 'whatever',
-              preferredValue: true,
-              preferredValueFromOrgLevel: false,
-              effectiveValue: true,
-              isOrgLevel: false,
-              effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
-              definition: {
-                ageBracket: {
-                  consentType: ESettingConsentType.OPT_OUT,
-                  defaultPreference: '',
-                },
-                orgId: '',
-                namespace: '',
-                settingName: '',
-                valueType: ESettingValueType.BOOLEAN,
-                translations: {},
-                restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
-                userHidden: false,
-                userReadOnly: false,
-                required: false,
-              },
-            },
+            makeUserSettingValue({ namespace: 'chat', settingName: 'voice', effectiveValue: true }),
+            makeUserSettingValue({ namespace: 'default', settingName: 'whatever', effectiveValue: true }),
           ],
           ['chat.voice', 'whatever'],
         ),
@@ -1560,63 +1308,33 @@
       expect(
         SettingsService.transformSettingsToGroupedPermissions(
           [
-            {
+            makeUserSettingValue({
               namespace: 'chat',
               settingName: 'voice',
-              preferredValue: true,
-              preferredValueFromOrgLevel: false,
               effectiveValue: true,
-              isOrgLevel: false,
-              definition: {
-                ageBracket: {
-                  consentType: ESettingConsentType.OPT_IN_VERIFIED,
-                },
-                translations: {
-                  en: {
-                    label: 'Voice chat',
-                    parentNotice: 'Allow user to use voice chat',
-                  },
-                },
-              },
-            } as unknown as UserSettingValueDTO,
-            {
+              definition: makeDefinition({
+                ageBracket: { consentType: ESettingConsentType.OPT_IN_VERIFIED, defaultPreference: '' },
+                translations: { en: { label: 'Voice chat', parentNotice: 'Allow user to use voice chat' } },
+              }),
+            }),
+            makeUserSettingValue({
               namespace: 'chat',
               settingName: 'text',
-              preferredValue: true,
-              preferredValueFromOrgLevel: false,
               effectiveValue: false,
-              isOrgLevel: false,
-              definition: {
-                ageBracket: {
-                  consentType: ESettingConsentType.OPT_IN_VERIFIED,
-                },
-                translations: {
-                  en: {
-                    label: 'Text chat',
-                    parentNotice: 'Allow user to use text chat',
-                  },
-                },
-              },
-            } as unknown as UserSettingValueDTO,
-            {
+              definition: makeDefinition({
+                ageBracket: { consentType: ESettingConsentType.OPT_IN_VERIFIED, defaultPreference: '' },
+                translations: { en: { label: 'Text chat', parentNotice: 'Allow user to use text chat' } },
+              }),
+            }),
+            makeUserSettingValue({
               namespace: 'default',
               settingName: 'whatever',
-              preferredValue: true,
-              preferredValueFromOrgLevel: false,
               effectiveValue: true,
-              isOrgLevel: false,
-              definition: {
-                ageBracket: {
-                  consentType: ESettingConsentType.OPT_OUT,
-                },
-                translations: {
-                  en: {
-                    label: 'Default permission',
-                    parentNotice: 'Allow user to do everything',
-                  },
-                },
-              },
-            } as unknown as UserSettingValueDTO,
+              definition: makeDefinition({
+                ageBracket: { consentType: ESettingConsentType.OPT_OUT, defaultPreference: '' },
+                translations: { en: { label: 'Default permission', parentNotice: 'Allow user to do everything' } },
+              }),
+            }),
           ],
           ['chat.voice', 'chat.text', 'default.whatever'],
         ),
@@ -1648,26 +1366,17 @@
       expect(
         SettingsService.transformSettingsToGroupedPermissions(
           [
-            {
+            makeUserSettingValue({
               namespace: 'default',
               settingName: 'optout-setting',
               preferredValue: false,
-              preferredValueFromOrgLevel: false,
               effectiveValue: false,
-              isOrgLevel: false,
-              parentLimitUpdatedAt: new Date(),
-              definition: {
-                ageBracket: {
-                  consentType: ESettingConsentType.OPT_OUT,
-                },
-                translations: {
-                  en: {
-                    label: 'Opt-out Setting',
-                    parentNotice: 'Opt-out setting description',
-                  },
-                },
-              },
-            } as unknown as UserSettingValueDTO,
+              parentLimitUpdatedAt: Date.now(),
+              definition: makeDefinition({
+                ageBracket: { consentType: ESettingConsentType.OPT_OUT, defaultPreference: '' },
+                translations: { en: { label: 'Opt-out Setting', parentNotice: 'Opt-out setting description' } },
+              }),
+            }),
           ],
           ['optout-setting'],
         ),
